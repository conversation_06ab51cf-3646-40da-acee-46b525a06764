# Pico Repo Layout

- `sdk` Provides user interface APIs for the projects, it includes a Cargo CLI for proving, an SDK for streamlined access, and patch interfaces. This structure simplifies building and managing workflows for the VM ecosystem.
- `examples`: Pico SDK examples, quick start to use the Pico SDK, and provide more advanced usage examples.
- `vm` is the main crate of the repository. It contains:
  - `examples` includes examples that demonstrate the usage of the VMs. Consult `examples/test_proverchain.rs` if you need to manually configure a particular proving stage, which also allows for the proving process to stop early.
  - `chips` includes various chips and related bits that could be (re)used by various VMs like RISC-V, Recursion, or any other Application-Specific VM (as precompiles or coprocessors).
  - `compiler` includes compilers as the application-specific component that compiles the source code (Rust codes, verifier circuits, etc.) to machine code that could be emulated by the target VM.
  - `configs` includes configuration files on different fields, different hash function used and different hyperparameters of proving protocol for VMs with different purposes or at different stages.
  - `emulator` includes emulators that take programs output by `compilers` as input and generate emulation records for proving by `machines`.
  - `instances` includes instantiations for `chiptype`, `compiler`, `config` and `machine`.
  - `machine` includes main proving logic of Pico.
  - `primitives` includes consts and types used across Pico.
  - `proverchain` includes proving phase logic of Pico.
- `docs` includes documentation during development of Pico.
- `gnark` Groth16 Verifiers for the Starks FRI generated by the Pico over KoalaBear or Babybear.
- `.github/workflows/rust.yml` includes Github tests before merging branches to main.
