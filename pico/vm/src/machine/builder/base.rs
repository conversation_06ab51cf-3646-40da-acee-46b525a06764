//! Basic AIR associating functions for the chip builder

use crate::machine::extension::BinomialExtension;
use core::marker::PhantomData;
use itertools::Itertools;
use p3_air::{AirBuilder, FilteredAirBuilder};
use p3_field::{Field, FieldAlgebra};
use std::array;

pub trait ChipBaseBuilder<F: Field>: AirBuilder<F = F> {
    /// Returns a sub-builder whose constraints are enforced only when `condition` is not one.
    fn when_not<I: Into<Self::Expr>>(&mut self, condition: I) -> FilteredAirBuilder<'_, Self> {
        self.when_ne(condition, Self::F::ONE)
    }

    /// Asserts that an iterator of expressions are all equal.
    fn assert_all_eq<I1: Into<Self::Expr>, I2: Into<Self::Expr>>(
        &mut self,
        left: impl IntoIterator<Item = I1>,
        right: impl IntoIterator<Item = I2>,
    ) {
        for (left, right) in left.into_iter().zip_eq(right) {
            self.assert_eq(left, right);
        }
    }

    /// Asserts that an iterator of expressions are all zero.
    fn assert_all_zero<I: Into<Self::Expr>>(&mut self, iter: impl IntoIterator<Item = I>) {
        iter.into_iter().for_each(|expr| self.assert_zero(expr));
    }

    /// Will return `a` if `condition` is 1, else `b`.  This assumes that `condition` is already
    /// checked to be a boolean.
    #[inline]
    fn if_else(
        &mut self,
        condition: impl Into<Self::Expr> + Clone,
        a: impl Into<Self::Expr> + Clone,
        b: impl Into<Self::Expr> + Clone,
    ) -> Self::Expr {
        condition.clone().into() * a.into() + (Self::Expr::ONE - condition.into()) * b.into()
    }

    /// Index an array of expressions using an index bitmap.  This function assumes that the
    /// `EIndex` type is a boolean and that `index_bitmap`'s entries sum to 1.
    fn index_array(
        &mut self,
        array: &[impl Into<Self::Expr> + Clone],
        index_bitmap: &[impl Into<Self::Expr> + Clone],
    ) -> Self::Expr {
        let mut result = Self::Expr::ZERO;

        for (value, i) in array.iter().zip_eq(index_bitmap) {
            result += value.clone().into() * i.clone().into();
        }

        result
    }

    // Extension field-related

    /// Asserts that the two field extensions are equal.
    fn assert_ext_eq<B, I: Into<Self::Expr>, const D: usize>(
        &mut self,
        left: BinomialExtension<B, I, D>,
        right: BinomialExtension<B, I, D>,
    ) {
        for (left, right) in left.0.into_iter().zip(right.0) {
            self.assert_eq(left, right);
        }
    }

    /// Checks if an extension element is a base element.
    fn assert_is_base_element<B, I: Into<Self::Expr> + Clone, const D: usize>(
        &mut self,
        element: BinomialExtension<B, I, D>,
    ) {
        let base_slice = element.as_base_slice();
        let degree = base_slice.len();
        base_slice[1..degree].iter().for_each(|coeff| {
            self.assert_zero(coeff.clone().into());
        });
    }

    /// Performs an if else on extension elements.
    fn if_else_ext<B, const D: usize>(
        &mut self,
        condition: impl Into<Self::Expr> + Clone,
        a: BinomialExtension<B, impl Into<Self::Expr> + Clone, D>,
        b: BinomialExtension<B, impl Into<Self::Expr> + Clone, D>,
    ) -> BinomialExtension<B, Self::Expr, D> {
        BinomialExtension(
            array::from_fn(|i| self.if_else(condition.clone(), a.0[i].clone(), b.0[i].clone())),
            PhantomData,
        )
    }
}
