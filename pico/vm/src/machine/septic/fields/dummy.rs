pub const TOP_BITS: usize = 0;

pub const EXT_COEFFS: [u32; 7] = [0, 0, 0, 0, 0, 0, 0];

pub const Z_POW_P: [[u32; 7]; 7] = [
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
];

pub const Z_POW_P2: [[u32; 7]; 7] = [
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0],
];

pub const CURVE_WITNESS_DUMMY_POINT_X: [u32; 7] = [0, 0, 0, 0, 0, 0, 0];

pub const CURVE_WITNESS_DUMMY_POINT_Y: [u32; 7] = [0, 0, 0, 0, 0, 0, 0];

pub const CURVE_CUMULATIVE_SUM_START_X: [u32; 7] = [0, 0, 0, 0, 0, 0, 0];

pub const CURVE_CUMULATIVE_SUM_START_Y: [u32; 7] = [0, 0, 0, 0, 0, 0, 0];

pub const DIGEST_SUM_START_X: [u32; 7] = [0, 0, 0, 0, 0, 0, 0];

pub const DIGEST_SUM_START_Y: [u32; 7] = [0, 0, 0, 0, 0, 0, 0];
