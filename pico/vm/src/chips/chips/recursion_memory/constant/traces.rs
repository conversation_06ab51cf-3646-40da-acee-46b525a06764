use super::{
    super::MemoryAccessCols,
    columns::{MemoryPreprocessedCols, NUM_MEM_INIT_COLS, NUM_MEM_PREPROCESSED_INIT_COLS},
    MemoryConstChip,
};
use crate::{
    compiler::recursion::{
        instruction::Instruction,
        program::RecursionProgram,
        types::{MemAccessKind, MemInstr},
    },
    emulator::recursion::emulator::RecursionRecord,
    iter::IntoPicoRefIterator,
    machine::{chip::ChipBehavior, utils::pad_to_power_of_two},
    primitives::consts::CONST_MEM_DATAPAR,
};
use itertools::Itertools;
use p3_field::PrimeField32;
use p3_matrix::dense::RowMajorMatrix;
use p3_maybe_rayon::prelude::ParallelIterator;
use std::{borrow::BorrowMut, iter::zip};

impl<F: PrimeField32> ChipBehavior<F> for MemoryConstChip<F> {
    type Record = RecursionRecord<F>;
    type Program = RecursionProgram<F>;

    fn name(&self) -> String {
        "MemoryConst".to_string()
    }

    fn preprocessed_width(&self) -> usize {
        NUM_MEM_PREPROCESSED_INIT_COLS
    }

    fn generate_preprocessed(&self, program: &Self::Program) -> Option<RowMajorMatrix<F>> {
        // First collect the filtered and mapped items
        let filtered_items: Vec<_> = program
            .instructions
            .pico_iter()
            .filter_map(|instruction| match instruction {
                Instruction::Mem(MemInstr {
                    addrs,
                    vals,
                    mult,
                    kind,
                }) => {
                    let mult = mult.to_owned();
                    let mult = match kind {
                        MemAccessKind::Read => -mult,
                        MemAccessKind::Write => mult,
                    };

                    Some((
                        vals.inner,
                        MemoryAccessCols {
                            addr: addrs.inner,
                            mult,
                        },
                    ))
                }
                _ => None,
            })
            .collect();

        // Then process in chunks
        let rows = filtered_items
            .chunks(CONST_MEM_DATAPAR)
            .map(|row_vs_as| {
                let mut row = [F::ZERO; NUM_MEM_PREPROCESSED_INIT_COLS];
                let cols: &mut MemoryPreprocessedCols<_> = row.as_mut_slice().borrow_mut();
                for (cell, access) in zip(&mut cols.values_and_accesses, row_vs_as) {
                    *cell = *access;
                }
                row
            })
            .collect_vec();

        // Convert the trace to a row major matrix.
        let mut trace = RowMajorMatrix::new(
            rows.into_iter().flatten().collect_vec(),
            NUM_MEM_PREPROCESSED_INIT_COLS,
        );

        // Pad the trace to a power of two based on shape, if available.
        let log_size = program.fixed_log2_rows(&self.name());
        pad_to_power_of_two::<NUM_MEM_PREPROCESSED_INIT_COLS, F>(&mut trace.values, log_size);

        Some(trace)
    }

    fn generate_main(&self, input: &Self::Record, _: &mut Self::Record) -> RowMajorMatrix<F> {
        // Match number of rows generated by the `.chunks` call in `generate_preprocessed`.
        let num_rows = input
            .mem_const_count
            .checked_sub(1)
            .map(|x| x / CONST_MEM_DATAPAR + 1)
            .unwrap_or_default();
        let rows = std::iter::repeat_n([F::ZERO; NUM_MEM_INIT_COLS], num_rows).collect_vec();

        // Convert the trace to a row major matrix.
        let mut trace =
            RowMajorMatrix::new(rows.into_iter().flatten().collect_vec(), NUM_MEM_INIT_COLS);

        // Pad the trace to a power of two based on shape, if available.
        let log_size = input.fixed_log2_rows(&self.name());
        pad_to_power_of_two::<NUM_MEM_INIT_COLS, F>(&mut trace.values, log_size);

        trace
    }

    fn is_active(&self, _record: &Self::Record) -> bool {
        true
    }

    fn local_only(&self) -> bool {
        true
    }
}
