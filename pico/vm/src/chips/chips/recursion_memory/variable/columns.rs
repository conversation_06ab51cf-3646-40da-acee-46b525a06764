use super::super::MemoryAccessCols;
use crate::{compiler::recursion::ir::Block, primitives::consts::VAR_MEM_DATAPAR};
use pico_derive::AlignedBorrow;

pub const NUM_MEM_INIT_COLS: usize = core::mem::size_of::<MemoryCols<u8>>();

#[derive(<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>, Copy)]
#[repr(C)]
pub struct MemoryCols<F: Copy> {
    pub values: [Block<F>; VAR_MEM_DATAPAR],
}

pub const NUM_MEM_PREPROCESSED_INIT_COLS: usize =
    core::mem::size_of::<MemoryPreprocessedCols<u8>>();

#[derive(<PERSON><PERSON>B<PERSON><PERSON>, <PERSON>bug, <PERSON><PERSON>, <PERSON><PERSON>)]
#[repr(C)]
pub struct MemoryPreprocessedCols<F: Copy> {
    pub accesses: [MemoryAccessCols<F>; VAR_MEM_DATAPAR],
}
