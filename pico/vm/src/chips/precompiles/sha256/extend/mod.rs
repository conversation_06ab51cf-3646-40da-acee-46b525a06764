use std::marker::PhantomData;

mod columns;
mod constraints;
mod flags;
mod trace;

#[derive(Default)]
pub struct ShaExtendChip<F> {
    _marker: PhantomData<F>,
}

pub fn sha_extend(w: &mut [u32]) {
    for i in 16..64 {
        let s0 = w[i - 15].rotate_right(7) ^ w[i - 15].rotate_right(18) ^ (w[i - 15] >> 3);
        let s1 = w[i - 2].rotate_right(17) ^ w[i - 2].rotate_right(19) ^ (w[i - 2] >> 10);
        w[i] = w[i - 16] + s0 + w[i - 7] + s1;
    }
}
