use super::{riscv::Riscv<PERSON><PERSON>, <PERSON><PERSON><PERSON>r, Prover<PERSON><PERSON><PERSON>};
use crate::{
    configs::{
        config::{StarkGenericConfig, Val},
        field_config::{Baby<PERSON>earSimple, KoalaBearSimple},
        stark_config::{BabyBearPoseidon2, KoalaBearPoseidon2},
    },
    emulator::{opts::EmulatorOpts, stdin::EmulatorStdin},
    instances::{
        chiptype::recursion_chiptype::RecursionChipType,
        compiler::{
            shapes::recursion_shape::RecursionShapeConfig,
            vk_merkle::{vk_verification_enabled, HasStaticVkManager},
        },
        machine::convert::ConvertMachine,
    },
    machine::{
        field::FieldSpecificPoseidon2Config,
        keys::<PERSON><PERSON><PERSON><PERSON><PERSON>,
        machine::{BaseMachine, MachineBehavior},
        proof::MetaProof,
        witness::ProvingWitness,
    },
    primitives::consts::{DIGEST_SIZE, EXTENSION_DEGREE, RECURSION_NUM_PVS},
};
use p3_field::{extension::BinomiallyExtendable, FieldAlgebra, PrimeField32};
use std::cell::Cell;

type RecursionChips<SC> = RecursionChipType<Val<SC>>;

pub struct ConvertProver<RiscvSC, SC>
where
    RiscvSC: StarkGenericConfig,
    Val<RiscvSC>: PrimeField32 + FieldSpecificPoseidon2Config,
    SC: StarkGenericConfig,
    Val<SC>: PrimeField32 + BinomiallyExtendable<EXTENSION_DEGREE> + FieldSpecificPoseidon2Config,
{
    machine: ConvertMachine<SC, RecursionChips<SC>>,
    opts: EmulatorOpts,
    shape_config: Option<RecursionShapeConfig<Val<SC>, RecursionChips<SC>>>,
    prev_machine: BaseMachine<RiscvSC, RiscvChips<RiscvSC>>,
    final_deferred_digest: Cell<[Val<SC>; DIGEST_SIZE]>,
}

macro_rules! impl_convert_prover {
    ($riscv_sc:ident, $recur_cc:ident, $recur_sc:ident) => {
        impl ProverChain<$riscv_sc, RiscvChips<$riscv_sc>, $recur_sc>
            for ConvertProver<$riscv_sc, $recur_sc>
        {
            type Opts = EmulatorOpts;
            type ShapeConfig = RecursionShapeConfig<Val<$recur_sc>, RecursionChips<$recur_sc>>;

            fn new_with_prev(
                prev_prover: &impl MachineProver<$riscv_sc, Chips = RiscvChips<$riscv_sc>>,
                opts: Self::Opts,
                shape_config: Option<Self::ShapeConfig>,
            ) -> Self {
                let machine = ConvertMachine::new(
                    $recur_sc::new(),
                    RecursionChips::<$recur_sc>::convert_chips(),
                    RECURSION_NUM_PVS,
                );
                Self {
                    machine,
                    opts,
                    shape_config,
                    prev_machine: prev_prover.machine().clone(),
                    final_deferred_digest: Cell::new([Val::<$recur_sc>::ZERO; DIGEST_SIZE]),
                }
            }
        }

        impl MachineProver<$recur_sc> for ConvertProver<$riscv_sc, $recur_sc> {
            type Witness = MetaProof<$riscv_sc>;
            type Chips = RecursionChips<$recur_sc>;

            fn machine(&self) -> &BaseMachine<$recur_sc, Self::Chips> {
                self.machine.base_machine()
            }

            fn prove(&self, proofs: Self::Witness) -> MetaProof<$recur_sc> {
                assert_eq!(proofs.vks.len(), 1);

                let vk_root = if self.shape_config.is_some() && vk_verification_enabled() {
                    let vk_manager = <$recur_sc as HasStaticVkManager>::static_vk_manager();
                    vk_manager.merkle_root
                } else {
                    [Val::<$riscv_sc>::ZERO; DIGEST_SIZE]
                };

                let stdin = EmulatorStdin::setup_for_convert::<Val<$recur_sc>, $recur_cc>(
                    &proofs.vks[0],
                    vk_root,
                    self.final_deferred_digest.get(),
                    &self.prev_machine,
                    &proofs.proofs(),
                    &self.shape_config,
                );
                let witness =
                    ProvingWitness::setup_for_convert(stdin, self.machine.config(), self.opts);
                self.machine.prove(&witness)
            }

            fn verify(
                &self,
                proof: &MetaProof<$recur_sc>,
                riscv_vk: &dyn HashableKey<Val<$recur_sc>>,
            ) -> bool {
                self.machine.verify(proof, riscv_vk).is_ok()
            }
        }

        impl ConvertProver<$riscv_sc, $recur_sc> {
            pub fn set_final_deferred_digest(&self, d: [Val<$recur_sc>; DIGEST_SIZE]) {
                self.final_deferred_digest.set(d);
            }

            pub fn final_deferred_digest(&self) -> [Val<$recur_sc>; DIGEST_SIZE] {
                self.final_deferred_digest.get()
            }
        }
    };
}

impl_convert_prover!(BabyBearPoseidon2, BabyBearSimple, BabyBearPoseidon2);
impl_convert_prover!(KoalaBearPoseidon2, KoalaBearSimple, KoalaBearPoseidon2);
