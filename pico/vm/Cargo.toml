[package]
name = "pico-vm"
version.workspace = true
edition.workspace = true
license.workspace = true
keywords.workspace = true
categories.workspace = true

[dependencies]
# pico
pico-derive.workspace = true

# p3
p3-air.workspace = true
p3-field.workspace = true
p3-commit.workspace = true
p3-matrix.workspace = true
p3-baby-bear.workspace = true
p3-util.workspace = true
p3-challenger.workspace = true
p3-dft.workspace = true
p3-fri.workspace = true
p3-goldilocks.workspace = true
p3-keccak.workspace = true
p3-keccak-air.workspace = true
p3-blake3.workspace = true
p3-mds.workspace = true
p3-merkle-tree.workspace = true
p3-poseidon2.workspace = true
p3-symmetric.workspace = true
p3-uni-stark.workspace = true
p3-maybe-rayon = { workspace = true, features = [] }
p3-bn254-fr.workspace = true
p3-mersenne-31.workspace = true
p3-circle.workspace = true
p3-koala-bear.workspace = true

# misc
amcl.workspace = true
anyhow.workspace = true
arrayref.workspace = true
backtrace.workspace = true
bincode.workspace = true
bytemuck.workspace = true
cfg-if.workspace = true
clap.workspace = true
cpu-time.workspace = true
crossbeam.workspace = true
core_affinity.workspace = true
csv.workspace = true
curve25519-dalek.workspace = true
dashmap.workspace = true
dashu.workspace = true
derive_more.workspace = true
elf.workspace = true
elliptic-curve.workspace = true
eyre.workspace = true
ff.workspace = true
halo2curves.workspace = true
hashbrown.workspace = true
hex.workspace = true
hybrid-array.workspace = true
itertools.workspace = true
k256.workspace = true
lazy_static.workspace = true
log.workspace = true
num-bigint.workspace = true
num-traits.workspace = true
num.workspace = true
num_cpus.workspace = true
once_cell.workspace = true
p256.workspace = true
paste.workspace = true
rand.workspace = true
rayon-scan = { workspace = true, optional = true }
rayon = { workspace = true, optional = true }
rrs_lib.workspace = true
rug = { workspace = true, optional = true }
serde.workspace = true
serde_json.workspace = true
serde_with.workspace = true
static_assertions.workspace = true
strum.workspace = true
strum_macros.workspace = true
sysinfo.workspace = true
thiserror.workspace = true
tiny-keccak.workspace = true
tracing-forest.workspace = true
tracing-subscriber.workspace = true
tracing.workspace = true
typenum.workspace = true
vec_map.workspace = true
zkhash.workspace = true


# Set JEMALLOC_SYS_WITH_MALLOC_CONF="retain:true,background_thread:true,metadata_thp:always,dirty_decay_ms:-1,muzzy_decay_ms:-1,abort_conf:true"
[target.'cfg(not(target_env = "msvc"))'.dependencies]
tikv-jemallocator = { workspace = true, optional = true }

[dev-dependencies]
num = { workspace = true, features = ["rand"] }
rand.workspace = true

[features]
default = ["rayon", "nightly-features", "strict"]
bigint-rug = ["rug"] # curves
debug = []
debug-lookups = []
jemalloc = ["dep:tikv-jemallocator"]
single-threaded = []
rayon = ["dep:rayon", "dep:rayon-scan", "p3-maybe-rayon/parallel"]
nightly-features = [
    "p3-dft/nightly-features",
    "p3-keccak/nightly-features",
    "p3-poseidon2/nightly-features",
]
strict = []
