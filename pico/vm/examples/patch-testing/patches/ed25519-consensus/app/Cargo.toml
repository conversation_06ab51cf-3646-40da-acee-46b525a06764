[package]
name = "patch-test-ed25519-consensus"
version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
keywords = { workspace = true }
categories = { workspace = true }

[dependencies]
pico-sdk = { workspace = true }
alloy-sol-types = { workspace = true }
ed25519-consensus = { git = "https://github.com/brevis-network/ed25519-consensus.git", branch = "patch-v1.0.1" }
