[package]
name = "tendermint-example-script"
version = "1.0.0"
edition = { workspace = true }
license = { workspace = true }
keywords = { workspace = true }
categories = { workspace = true }

[dependencies]
pico-sdk = { path = "../../../sdk/sdk" }
tendermint-light-client-verifier = { version = "0.35.0", default-features = false, features = [
  "rust-crypto",
] }
serde_cbor = "0.11.2"
serde_json = "1.0.121"
