[package]
name = "pico-scripts"
version.workspace = true
edition.workspace = true

[dependencies]
pico-vm.workspace = true

# p3
p3-field.workspace = true
p3-baby-bear.workspace = true
p3-koala-bear.workspace = true

# misc
bincode.workspace = true
hashbrown.workspace = true
rayon.workspace = true
clap = {workspace = true, features = ["derive"]}


[[bin]]
name = "build_vk_map"
path = "src/bin/build_vk_map.rs"
