use std::{
    io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>uf<PERSON><PERSON><PERSON>},
    process::{Command, Stdio},
    thread,
};

use anyhow::Context;

pub fn execute_command(mut command: Command) {
    println!("Start to execute command...");
    log_command(&command);
    // Add necessary tags for stdout and stderr from the command.
    let mut child = command
        .stdout(Stdio::piped())
        .stderr(Stdio::piped())
        .spawn()
        .context("failed to spawn command")
        .expect("cargo build failed");
    let stdout = BufReader::new(child.stdout.take().unwrap());
    let stderr = BufReader::new(child.stderr.take().unwrap());

    // Add prefix to the output of the process depending on the context.
    let msg = "[pico]";

    // Pipe stdout and stderr to the parent process with [docker] prefix
    let stdout_handle = thread::spawn(move || {
        stdout.lines().for_each(|line| {
            println!("{} {}", msg, line.unwrap());
        });
    });
    stderr.lines().for_each(|line| {
        eprintln!("{} {}", msg, line.unwrap());
    });
    stdout_handle.join().unwrap();

    let _ = child.wait().expect("failed to wait for child process");
}

fn log_command(command: &Command) {
    let command_string = format!(
        "{} {}",
        command.get_program().to_string_lossy(),
        command
            .get_args()
            .map(|arg| arg.to_string_lossy())
            .collect::<Vec<_>>()
            .join(" ")
    );
    println!("Command: {:?}", command_string);
}
