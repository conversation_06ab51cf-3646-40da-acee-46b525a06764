[package]
name = "pico-sdk"
version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
repository = { workspace = true }
keywords = { workspace = true }
categories = { workspace = true }

[dependencies]
bincode.workspace = true
serde.workspace = true
anyhow.workspace = true
p3-baby-bear.workspace = true
p3-challenger.workspace = true
p3-field.workspace = true
p3-koala-bear.workspace = true
p3-mersenne-31.workspace = true
pico-vm.workspace = true
pico-patch-libs.workspace = true
cfg-if.workspace = true
rand.workspace = true
log.workspace = true
env_logger.workspace = true
lazy_static.workspace = true
getrandom = { version = "0.2.15", features = ["custom"] }
libm = { version = "0.2.8", optional = true }
sha2 = { version = "0.10.8" }
coprocessor-sdk = { git = "https://github.com/brevis-network/Pico-zkCoprocessor", optional = true }
hex.workspace = true
serde_json.workspace = true

[features]
default = ["kb"]
prover = ["pico-vm/jemalloc", "pico-vm/nightly-features"]
libm = ["dep:libm"]
coprocessor = ["coprocessor-sdk"]
bb = []     # BabyBear
kb = []     # KoalaBear
m31 = []    # Mersenne31
