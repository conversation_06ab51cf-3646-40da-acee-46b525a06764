[package]
name = "pico-cli"
version = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
repository = { workspace = true }
keywords = { workspace = true }
categories = { workspace = true }

[build-dependencies]
vergen = { version = "8", default-features = false, features = [
    "build",
    "git",
    "git2",
] }

[dependencies]
clap = {workspace = true, features = ["cargo", "derive", "env"]}
anyhow.workspace = true
hex.workspace = true
pico-sdk.workspace = true
log.workspace = true
env_logger.workspace = true
pico-vm.workspace = true
yansi = "1.0.1"
cargo_metadata = "0.18.1"
serde_json.workspace = true
serde.workspace = true
num-bigint.workspace = true
num-traits.workspace = true
