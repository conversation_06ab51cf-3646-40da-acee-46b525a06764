[workspace]
members = [
    "reth-pico",
    "fibon<PERSON>ci"
]
resolver = "2"

[workspace.dependencies]
pico-sdk = { path = "../../sdk/sdk"}

[patch."https://github.com/brevis-network/pico"]
pico-patch-libs = { path = "../../sdk/patch-libs" }

[patch.crates-io]
# precompile patches
sha2 = { git = "https://github.com/brevis-network/hashes.git", package = "sha2", branch = "sha2-0.10.8-v1.0.0"}
curve25519-dalek-ng = { git = "https://github.com/brevis-network/curve25519-dalek-ng", branch = "patch-v1.0.1"  }
ecdsa-core = { git = "https://github.com/brevis-network/signatures", package = "ecdsa", branch = "patch-v1.0.1" }
tiny-keccak = { git = "https://github.com/brevis-network/tiny-keccak", branch = "patch-v1.0.0" }
bn = { git = "https://github.com/brevis-network/bn", branch = "patch-v1.0.1", package = "substrate-bn" }
bls12_381 = { git = "https://github.com/brevis-network/bls12_381", branch = "patch-v1.0.1" }
kzg-rs = { git = "https://github.com/brevis-network/kzg-rs.git", branch = "pico" }
sha3 = { git = "https://github.com/brevis-network/hashes.git", branch = "sha3-0.10.8-v1.0.0"}

# elf optimization
[profile.release]
opt-level = 3
lto = "fat"
codegen-units = 1
debug = true
embed-bitcode = true