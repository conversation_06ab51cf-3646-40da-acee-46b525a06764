/home/<USER>/2025-08-pico-x15-eth/pico/target/debug/deps/pico_vm-0cbd12e0bc32786e.d: vm/src/lib.rs vm/src/chips/mod.rs vm/src/chips/chips/mod.rs vm/src/chips/chips/alu/mod.rs vm/src/chips/chips/alu/add_sub/mod.rs vm/src/chips/chips/alu/add_sub/columns.rs vm/src/chips/chips/alu/add_sub/constraints.rs vm/src/chips/chips/alu/add_sub/traces.rs vm/src/chips/chips/alu/bitwise/mod.rs vm/src/chips/chips/alu/bitwise/columns.rs vm/src/chips/chips/alu/bitwise/constraints.rs vm/src/chips/chips/alu/bitwise/traces.rs vm/src/chips/chips/alu/divrem/mod.rs vm/src/chips/chips/alu/divrem/columns.rs vm/src/chips/chips/alu/divrem/constraints.rs vm/src/chips/chips/alu/divrem/traces.rs vm/src/chips/chips/alu/divrem/utils.rs vm/src/chips/chips/alu/event.rs vm/src/chips/chips/alu/lt/mod.rs vm/src/chips/chips/alu/lt/columns.rs vm/src/chips/chips/alu/lt/constraints.rs vm/src/chips/chips/alu/lt/traces.rs vm/src/chips/chips/alu/mul/mod.rs vm/src/chips/chips/alu/mul/columns.rs vm/src/chips/chips/alu/mul/constraints.rs vm/src/chips/chips/alu/mul/traces.rs vm/src/chips/chips/alu/sll/mod.rs vm/src/chips/chips/alu/sll/columns.rs vm/src/chips/chips/alu/sll/constraints.rs vm/src/chips/chips/alu/sll/traces.rs vm/src/chips/chips/alu/sr/mod.rs vm/src/chips/chips/alu/sr/columns.rs vm/src/chips/chips/alu/sr/constraints.rs vm/src/chips/chips/alu/sr/traces.rs vm/src/chips/chips/byte/mod.rs vm/src/chips/chips/byte/columns.rs vm/src/chips/chips/byte/constraints.rs vm/src/chips/chips/byte/event.rs vm/src/chips/chips/byte/traces.rs vm/src/chips/chips/byte/utils.rs vm/src/chips/chips/riscv_cpu/mod.rs vm/src/chips/chips/riscv_cpu/auipc/mod.rs vm/src/chips/chips/riscv_cpu/auipc/columns.rs vm/src/chips/chips/riscv_cpu/auipc/constraints.rs vm/src/chips/chips/riscv_cpu/auipc/traces.rs vm/src/chips/chips/riscv_cpu/branch/mod.rs vm/src/chips/chips/riscv_cpu/branch/columns.rs vm/src/chips/chips/riscv_cpu/branch/constraints.rs vm/src/chips/chips/riscv_cpu/branch/traces.rs vm/src/chips/chips/riscv_cpu/chunk_clk/mod.rs vm/src/chips/chips/riscv_cpu/chunk_clk/constraints.rs vm/src/chips/chips/riscv_cpu/chunk_clk/traces.rs vm/src/chips/chips/riscv_cpu/columns.rs vm/src/chips/chips/riscv_cpu/constraints.rs vm/src/chips/chips/riscv_cpu/ecall/mod.rs vm/src/chips/chips/riscv_cpu/ecall/columns.rs vm/src/chips/chips/riscv_cpu/ecall/constraints.rs vm/src/chips/chips/riscv_cpu/ecall/traces.rs vm/src/chips/chips/riscv_cpu/event.rs vm/src/chips/chips/riscv_cpu/instruction/mod.rs vm/src/chips/chips/riscv_cpu/instruction/columns.rs vm/src/chips/chips/riscv_cpu/jump/mod.rs vm/src/chips/chips/riscv_cpu/jump/columns.rs vm/src/chips/chips/riscv_cpu/jump/constraints.rs vm/src/chips/chips/riscv_cpu/jump/traces.rs vm/src/chips/chips/riscv_cpu/opcode_selector/mod.rs vm/src/chips/chips/riscv_cpu/opcode_selector/columns.rs vm/src/chips/chips/riscv_cpu/opcode_specific/mod.rs vm/src/chips/chips/riscv_cpu/opcode_specific/columns.rs vm/src/chips/chips/riscv_cpu/public_values/mod.rs vm/src/chips/chips/riscv_cpu/public_values/constraints.rs vm/src/chips/chips/riscv_cpu/register/mod.rs vm/src/chips/chips/riscv_cpu/register/constraints.rs vm/src/chips/chips/riscv_cpu/traces.rs vm/src/chips/chips/riscv_cpu/utils.rs vm/src/chips/chips/riscv_global/mod.rs vm/src/chips/chips/riscv_global/columns.rs vm/src/chips/chips/riscv_global/constraints.rs vm/src/chips/chips/riscv_global/event.rs vm/src/chips/chips/riscv_global/traces.rs vm/src/chips/chips/riscv_memory/mod.rs vm/src/chips/chips/riscv_memory/event.rs vm/src/chips/chips/riscv_memory/initialize_finalize/mod.rs vm/src/chips/chips/riscv_memory/initialize_finalize/columns.rs vm/src/chips/chips/riscv_memory/initialize_finalize/constraints.rs vm/src/chips/chips/riscv_memory/initialize_finalize/traces.rs vm/src/chips/chips/riscv_memory/local/mod.rs vm/src/chips/chips/riscv_memory/local/columns.rs vm/src/chips/chips/riscv_memory/local/constraints.rs vm/src/chips/chips/riscv_memory/local/traces.rs vm/src/chips/chips/riscv_memory/read_write/mod.rs vm/src/chips/chips/riscv_memory/read_write/columns.rs vm/src/chips/chips/riscv_memory/read_write/constraints.rs vm/src/chips/chips/riscv_memory/read_write/traces.rs vm/src/chips/chips/riscv_poseidon2/mod.rs vm/src/chips/chips/riscv_poseidon2/constraints.rs vm/src/chips/chips/riscv_poseidon2/event.rs vm/src/chips/chips/riscv_poseidon2/traces.rs vm/src/chips/chips/riscv_program/mod.rs vm/src/chips/chips/riscv_program/columns.rs vm/src/chips/chips/riscv_program/constraints.rs vm/src/chips/chips/riscv_program/traces.rs vm/src/chips/chips/toys/mod.rs vm/src/chips/chips/toys/lookup_toy.rs vm/src/chips/chips/toys/toy.rs vm/src/chips/chips/events.rs vm/src/chips/chips/alu_base/mod.rs vm/src/chips/chips/alu_base/columns.rs vm/src/chips/chips/alu_base/constraints.rs vm/src/chips/chips/alu_base/traces.rs vm/src/chips/chips/alu_ext/mod.rs vm/src/chips/chips/alu_ext/columns.rs vm/src/chips/chips/alu_ext/constraints.rs vm/src/chips/chips/alu_ext/traces.rs vm/src/chips/chips/batch_fri/mod.rs vm/src/chips/chips/batch_fri/columns.rs vm/src/chips/chips/batch_fri/constraints.rs vm/src/chips/chips/batch_fri/traces.rs vm/src/chips/chips/exp_reverse_bits/mod.rs vm/src/chips/chips/exp_reverse_bits/columns.rs vm/src/chips/chips/exp_reverse_bits/constraints.rs vm/src/chips/chips/exp_reverse_bits/traces.rs vm/src/chips/chips/poseidon2/mod.rs vm/src/chips/chips/poseidon2/constraints.rs vm/src/chips/chips/poseidon2/traces.rs vm/src/chips/chips/public_values/mod.rs vm/src/chips/chips/public_values/columns.rs vm/src/chips/chips/public_values/constraints.rs vm/src/chips/chips/public_values/traces.rs vm/src/chips/chips/recursion_memory/mod.rs vm/src/chips/chips/recursion_memory/constant/mod.rs vm/src/chips/chips/recursion_memory/constant/columns.rs vm/src/chips/chips/recursion_memory/constant/constraints.rs vm/src/chips/chips/recursion_memory/constant/traces.rs vm/src/chips/chips/recursion_memory/variable/mod.rs vm/src/chips/chips/recursion_memory/variable/columns.rs vm/src/chips/chips/recursion_memory/variable/constraints.rs vm/src/chips/chips/recursion_memory/variable/traces.rs vm/src/chips/chips/select/mod.rs vm/src/chips/chips/select/columns.rs vm/src/chips/chips/select/constraints.rs vm/src/chips/chips/select/trace.rs vm/src/chips/chips/syscall/mod.rs vm/src/chips/chips/syscall/columns.rs vm/src/chips/chips/syscall/constraints.rs vm/src/chips/chips/syscall/traces.rs vm/src/chips/gadgets/mod.rs vm/src/chips/gadgets/add.rs vm/src/chips/gadgets/add4.rs vm/src/chips/gadgets/add5.rs vm/src/chips/gadgets/and.rs vm/src/chips/gadgets/curves/mod.rs vm/src/chips/gadgets/curves/edwards/mod.rs vm/src/chips/gadgets/curves/edwards/ed25519.rs vm/src/chips/gadgets/curves/scalar_mul.rs vm/src/chips/gadgets/curves/weierstrass/mod.rs vm/src/chips/gadgets/curves/weierstrass/bls381.rs vm/src/chips/gadgets/curves/weierstrass/bn254.rs vm/src/chips/gadgets/curves/weierstrass/secp256k1.rs vm/src/chips/gadgets/field/mod.rs vm/src/chips/gadgets/field/bls381.rs vm/src/chips/gadgets/field/bn254.rs vm/src/chips/gadgets/field/field_den.rs vm/src/chips/gadgets/field/field_inner_product.rs vm/src/chips/gadgets/field/field_lt.rs vm/src/chips/gadgets/field/field_op.rs vm/src/chips/gadgets/field/field_sqrt.rs vm/src/chips/gadgets/field/secp256k1.rs vm/src/chips/gadgets/field/utils.rs vm/src/chips/gadgets/field_range_check/mod.rs vm/src/chips/gadgets/field_range_check/bit_decomposition.rs vm/src/chips/gadgets/field_range_check/word_range.rs vm/src/chips/gadgets/fixed_rotate_right.rs vm/src/chips/gadgets/fixed_shift_right.rs vm/src/chips/gadgets/global_accumulation.rs vm/src/chips/gadgets/global_interaction.rs vm/src/chips/gadgets/is_equal_word.rs vm/src/chips/gadgets/is_zero.rs vm/src/chips/gadgets/is_zero_word.rs vm/src/chips/gadgets/lt.rs vm/src/chips/gadgets/not.rs vm/src/chips/gadgets/poseidon2/mod.rs vm/src/chips/gadgets/poseidon2/columns.rs vm/src/chips/gadgets/poseidon2/constants.rs vm/src/chips/gadgets/poseidon2/constraints.rs vm/src/chips/gadgets/poseidon2/traces.rs vm/src/chips/gadgets/poseidon2/utils.rs vm/src/chips/gadgets/uint256/mod.rs vm/src/chips/gadgets/utils/mod.rs vm/src/chips/gadgets/utils/conversions.rs vm/src/chips/gadgets/utils/field_params.rs vm/src/chips/gadgets/utils/limbs.rs vm/src/chips/gadgets/utils/polynomial.rs vm/src/chips/gadgets/xor.rs vm/src/chips/precompiles/mod.rs vm/src/chips/precompiles/edwards/mod.rs vm/src/chips/precompiles/edwards/ed_add.rs vm/src/chips/precompiles/edwards/ed_decompress.rs vm/src/chips/precompiles/fptower/mod.rs vm/src/chips/precompiles/fptower/fp.rs vm/src/chips/precompiles/fptower/fp2_addsub.rs vm/src/chips/precompiles/fptower/fp2_mul.rs vm/src/chips/precompiles/keccak256/mod.rs vm/src/chips/precompiles/keccak256/columns.rs vm/src/chips/precompiles/keccak256/constraint.rs vm/src/chips/precompiles/keccak256/traces.rs vm/src/chips/precompiles/poseidon2/mod.rs vm/src/chips/precompiles/poseidon2/columns.rs vm/src/chips/precompiles/poseidon2/constraints.rs vm/src/chips/precompiles/poseidon2/traces.rs vm/src/chips/precompiles/sha256/mod.rs vm/src/chips/precompiles/sha256/compress/mod.rs vm/src/chips/precompiles/sha256/compress/columns.rs vm/src/chips/precompiles/sha256/compress/constraints.rs vm/src/chips/precompiles/sha256/compress/trace.rs vm/src/chips/precompiles/sha256/extend/mod.rs vm/src/chips/precompiles/sha256/extend/columns.rs vm/src/chips/precompiles/sha256/extend/constraints.rs vm/src/chips/precompiles/sha256/extend/flags.rs vm/src/chips/precompiles/sha256/extend/trace.rs vm/src/chips/precompiles/uint256/mod.rs vm/src/chips/precompiles/uint256/columns.rs vm/src/chips/precompiles/uint256/constraints.rs vm/src/chips/precompiles/uint256/traces.rs vm/src/chips/precompiles/weierstrass/mod.rs vm/src/chips/precompiles/weierstrass/weierstrass_add.rs vm/src/chips/precompiles/weierstrass/weierstrass_decompress.rs vm/src/chips/precompiles/weierstrass/weierstrass_double.rs vm/src/chips/trace.rs vm/src/chips/utils.rs vm/src/compiler/mod.rs vm/src/compiler/program.rs vm/src/compiler/recursion/mod.rs vm/src/compiler/recursion/circuit/mod.rs vm/src/compiler/recursion/circuit/builder.rs vm/src/compiler/recursion/circuit/challenger.rs vm/src/compiler/recursion/circuit/config.rs vm/src/compiler/recursion/circuit/constraints.rs vm/src/compiler/recursion/circuit/domain.rs vm/src/compiler/recursion/circuit/fri.rs vm/src/compiler/recursion/circuit/hash.rs vm/src/compiler/recursion/circuit/merkle_tree.rs vm/src/compiler/recursion/circuit/stark.rs vm/src/compiler/recursion/circuit/types.rs vm/src/compiler/recursion/circuit/utils.rs vm/src/compiler/recursion/circuit/witness/mod.rs vm/src/compiler/recursion/circuit/witness/embed.rs vm/src/compiler/recursion/circuit/witness/stark.rs vm/src/compiler/recursion/circuit/witness/witnessable.rs vm/src/compiler/recursion/constraints/mod.rs vm/src/compiler/recursion/constraints/opcodes.rs vm/src/compiler/recursion/instruction.rs vm/src/compiler/recursion/ir/mod.rs vm/src/compiler/recursion/ir/arithmetic.rs vm/src/compiler/recursion/ir/bits.rs vm/src/compiler/recursion/ir/block.rs vm/src/compiler/recursion/ir/builder.rs vm/src/compiler/recursion/ir/collections.rs vm/src/compiler/recursion/ir/compiler.rs vm/src/compiler/recursion/ir/instructions.rs vm/src/compiler/recursion/ir/ptr.rs vm/src/compiler/recursion/ir/symbolic.rs vm/src/compiler/recursion/ir/types.rs vm/src/compiler/recursion/ir/utils.rs vm/src/compiler/recursion/ir/var.rs vm/src/compiler/recursion/program.rs vm/src/compiler/recursion/types.rs vm/src/compiler/riscv/mod.rs vm/src/compiler/riscv/compiler.rs vm/src/compiler/riscv/disassembler/mod.rs vm/src/compiler/riscv/disassembler/elf.rs vm/src/compiler/riscv/disassembler/rrs.rs vm/src/compiler/riscv/instruction.rs vm/src/compiler/riscv/opcode.rs vm/src/compiler/riscv/program.rs vm/src/compiler/riscv/register.rs vm/src/compiler/word.rs vm/src/configs/mod.rs vm/src/configs/config.rs vm/src/configs/field_config/mod.rs vm/src/configs/field_config/bb_bn254.rs vm/src/configs/field_config/bb_simple.rs vm/src/configs/field_config/kb_bn254.rs vm/src/configs/field_config/kb_simple.rs vm/src/configs/stark_config/mod.rs vm/src/configs/stark_config/bb_bn254_poseidon2.rs vm/src/configs/stark_config/bb_poseidon2.rs vm/src/configs/stark_config/kb_bn254_poseidon2.rs vm/src/configs/stark_config/kb_poseidon2.rs vm/src/configs/stark_config/m31_poseidon2.rs vm/src/emulator/mod.rs vm/src/emulator/emulator.rs vm/src/emulator/opts.rs vm/src/emulator/record.rs vm/src/emulator/recursion/mod.rs vm/src/emulator/recursion/emulator/mod.rs vm/src/emulator/recursion/emulator/memory.rs vm/src/emulator/recursion/emulator/opcode.rs vm/src/emulator/recursion/public_values.rs vm/src/emulator/recursion/record.rs vm/src/emulator/riscv/mod.rs vm/src/emulator/riscv/emulator/mod.rs vm/src/emulator/riscv/emulator/error.rs vm/src/emulator/riscv/emulator/instruction.rs vm/src/emulator/riscv/emulator/instruction_simple.rs vm/src/emulator/riscv/emulator/mode.rs vm/src/emulator/riscv/emulator/unconstrained.rs vm/src/emulator/riscv/emulator/util.rs vm/src/emulator/riscv/hook/mod.rs vm/src/emulator/riscv/hook/ecrecover.rs vm/src/emulator/riscv/hook/ed_decompress.rs vm/src/emulator/riscv/memory.rs vm/src/emulator/riscv/public_values.rs vm/src/emulator/riscv/record.rs vm/src/emulator/riscv/state.rs vm/src/emulator/riscv/syscalls/mod.rs vm/src/emulator/riscv/syscalls/code.rs vm/src/emulator/riscv/syscalls/commit.rs vm/src/emulator/riscv/syscalls/deferred.rs vm/src/emulator/riscv/syscalls/halt.rs vm/src/emulator/riscv/syscalls/hint.rs vm/src/emulator/riscv/syscalls/precompiles/mod.rs vm/src/emulator/riscv/syscalls/precompiles/ec/mod.rs vm/src/emulator/riscv/syscalls/precompiles/ec/event.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/mod.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/add.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/decompress.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/event.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/mod.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/event.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_addsub.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_mul.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/event.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/permute.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/mod.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/event.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/permute.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/compress.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/event.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/extend.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/event.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/syscall.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/mod.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/add.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/decompress.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/double.rs vm/src/emulator/riscv/syscalls/syscall_context.rs vm/src/emulator/riscv/syscalls/unconstrained.rs vm/src/emulator/riscv/syscalls/verify.rs vm/src/emulator/riscv/syscalls/write.rs vm/src/emulator/stdin.rs vm/src/instances/mod.rs vm/src/instances/chiptype/mod.rs vm/src/instances/chiptype/chiptype_macros/mod.rs vm/src/instances/chiptype/chiptype_macros/define_chip_type.rs vm/src/instances/chiptype/chiptype_macros/enum_chip_type.rs vm/src/instances/chiptype/chiptype_macros/impl_air.rs vm/src/instances/chiptype/chiptype_macros/impl_base_air.rs vm/src/instances/chiptype/chiptype_macros/impl_chip_behavior.rs vm/src/instances/chiptype/recursion_chiptype.rs vm/src/instances/chiptype/riscv_chiptype.rs vm/src/instances/compiler/mod.rs vm/src/instances/compiler/onchain_circuit/mod.rs vm/src/instances/compiler/onchain_circuit/gnark/mod.rs vm/src/instances/compiler/onchain_circuit/gnark/builder.rs vm/src/instances/compiler/onchain_circuit/gnark/witness.rs vm/src/instances/compiler/onchain_circuit/stdin.rs vm/src/instances/compiler/onchain_circuit/utils.rs vm/src/instances/compiler/recursion_circuit/mod.rs vm/src/instances/compiler/recursion_circuit/combine/mod.rs vm/src/instances/compiler/recursion_circuit/combine/builder.rs vm/src/instances/compiler/recursion_circuit/compress/mod.rs vm/src/instances/compiler/recursion_circuit/compress/builder.rs vm/src/instances/compiler/recursion_circuit/embed/mod.rs vm/src/instances/compiler/recursion_circuit/embed/builder.rs vm/src/instances/compiler/recursion_circuit/stdin.rs vm/src/instances/compiler/riscv_circuit/mod.rs vm/src/instances/compiler/riscv_circuit/convert/mod.rs vm/src/instances/compiler/riscv_circuit/convert/builder.rs vm/src/instances/compiler/riscv_circuit/deferred/mod.rs vm/src/instances/compiler/riscv_circuit/deferred/builder.rs vm/src/instances/compiler/riscv_circuit/stdin.rs vm/src/instances/compiler/shapes/mod.rs vm/src/instances/compiler/shapes/recursion_shape.rs vm/src/instances/compiler/shapes/riscv_shape.rs vm/src/instances/compiler/simple_circuit/mod.rs vm/src/instances/compiler/simple_circuit/builder.rs vm/src/instances/compiler/simple_circuit/stdin.rs vm/src/instances/compiler/vk_merkle/mod.rs vm/src/instances/compiler/vk_merkle/builder.rs vm/src/instances/compiler/vk_merkle/stdin.rs vm/src/instances/compiler/witness.rs vm/src/instances/configs/mod.rs vm/src/instances/configs/embed_bb_bn254_poseidon2.rs vm/src/instances/configs/embed_kb_bn254_poseidon2.rs vm/src/instances/configs/recur_bb_poseidon2.rs vm/src/instances/configs/recur_kb_poseidon2.rs vm/src/instances/configs/riscv_bb_poseidon2.rs vm/src/instances/configs/riscv_kb_poseidon2.rs vm/src/instances/configs/riscv_m31_poseidon2.rs vm/src/instances/machine/mod.rs vm/src/instances/machine/combine.rs vm/src/instances/machine/compress.rs vm/src/instances/machine/convert.rs vm/src/instances/machine/deferred.rs vm/src/instances/machine/embed.rs vm/src/instances/machine/riscv.rs vm/src/instances/machine/simple.rs vm/src/iter/mod.rs vm/src/iter/rayon/mod.rs vm/src/iter/rayon/impls.rs vm/src/machine/mod.rs vm/src/machine/builder/mod.rs vm/src/machine/builder/base.rs vm/src/machine/builder/extension.rs vm/src/machine/builder/lookup.rs vm/src/machine/builder/permutation.rs vm/src/machine/builder/public_values.rs vm/src/machine/builder/range_check.rs vm/src/machine/builder/recursion.rs vm/src/machine/builder/riscv_memory.rs vm/src/machine/builder/scoped.rs vm/src/machine/builder/septic.rs vm/src/machine/builder/sub_builder.rs vm/src/machine/builder/word.rs vm/src/machine/chip.rs vm/src/machine/debug/mod.rs vm/src/machine/debug/constraints.rs vm/src/machine/debug/lookups.rs vm/src/machine/extension.rs vm/src/machine/field.rs vm/src/machine/folder.rs vm/src/machine/keys.rs vm/src/machine/logger.rs vm/src/machine/lookup.rs vm/src/machine/machine.rs vm/src/machine/permutation.rs vm/src/machine/proof.rs vm/src/machine/prover.rs vm/src/machine/septic/mod.rs vm/src/machine/septic/curve.rs vm/src/machine/septic/digest.rs vm/src/machine/septic/extension.rs vm/src/machine/septic/fields/mod.rs vm/src/machine/septic/fields/babybear.rs vm/src/machine/septic/fields/dummy.rs vm/src/machine/septic/fields/koalabear.rs vm/src/machine/septic/fields/mersenne31.rs vm/src/machine/utils.rs vm/src/machine/verifier.rs vm/src/machine/witness.rs vm/src/primitives/mod.rs vm/src/primitives/consts.rs vm/src/primitives/poseidon2/mod.rs vm/src/primitives/poseidon2/babybear.rs vm/src/primitives/poseidon2/koalabear.rs vm/src/primitives/poseidon2/mersenne31.rs vm/src/proverchain/mod.rs vm/src/proverchain/combine.rs vm/src/proverchain/compress.rs vm/src/proverchain/convert.rs vm/src/proverchain/embed.rs vm/src/proverchain/riscv.rs vm/src/proverchain/deferred.rs vm/src/thread/mod.rs vm/src/thread/channel.rs vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_bb.bin vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_kb.bin

/home/<USER>/2025-08-pico-x15-eth/pico/target/debug/deps/libpico_vm-0cbd12e0bc32786e.rlib: vm/src/lib.rs vm/src/chips/mod.rs vm/src/chips/chips/mod.rs vm/src/chips/chips/alu/mod.rs vm/src/chips/chips/alu/add_sub/mod.rs vm/src/chips/chips/alu/add_sub/columns.rs vm/src/chips/chips/alu/add_sub/constraints.rs vm/src/chips/chips/alu/add_sub/traces.rs vm/src/chips/chips/alu/bitwise/mod.rs vm/src/chips/chips/alu/bitwise/columns.rs vm/src/chips/chips/alu/bitwise/constraints.rs vm/src/chips/chips/alu/bitwise/traces.rs vm/src/chips/chips/alu/divrem/mod.rs vm/src/chips/chips/alu/divrem/columns.rs vm/src/chips/chips/alu/divrem/constraints.rs vm/src/chips/chips/alu/divrem/traces.rs vm/src/chips/chips/alu/divrem/utils.rs vm/src/chips/chips/alu/event.rs vm/src/chips/chips/alu/lt/mod.rs vm/src/chips/chips/alu/lt/columns.rs vm/src/chips/chips/alu/lt/constraints.rs vm/src/chips/chips/alu/lt/traces.rs vm/src/chips/chips/alu/mul/mod.rs vm/src/chips/chips/alu/mul/columns.rs vm/src/chips/chips/alu/mul/constraints.rs vm/src/chips/chips/alu/mul/traces.rs vm/src/chips/chips/alu/sll/mod.rs vm/src/chips/chips/alu/sll/columns.rs vm/src/chips/chips/alu/sll/constraints.rs vm/src/chips/chips/alu/sll/traces.rs vm/src/chips/chips/alu/sr/mod.rs vm/src/chips/chips/alu/sr/columns.rs vm/src/chips/chips/alu/sr/constraints.rs vm/src/chips/chips/alu/sr/traces.rs vm/src/chips/chips/byte/mod.rs vm/src/chips/chips/byte/columns.rs vm/src/chips/chips/byte/constraints.rs vm/src/chips/chips/byte/event.rs vm/src/chips/chips/byte/traces.rs vm/src/chips/chips/byte/utils.rs vm/src/chips/chips/riscv_cpu/mod.rs vm/src/chips/chips/riscv_cpu/auipc/mod.rs vm/src/chips/chips/riscv_cpu/auipc/columns.rs vm/src/chips/chips/riscv_cpu/auipc/constraints.rs vm/src/chips/chips/riscv_cpu/auipc/traces.rs vm/src/chips/chips/riscv_cpu/branch/mod.rs vm/src/chips/chips/riscv_cpu/branch/columns.rs vm/src/chips/chips/riscv_cpu/branch/constraints.rs vm/src/chips/chips/riscv_cpu/branch/traces.rs vm/src/chips/chips/riscv_cpu/chunk_clk/mod.rs vm/src/chips/chips/riscv_cpu/chunk_clk/constraints.rs vm/src/chips/chips/riscv_cpu/chunk_clk/traces.rs vm/src/chips/chips/riscv_cpu/columns.rs vm/src/chips/chips/riscv_cpu/constraints.rs vm/src/chips/chips/riscv_cpu/ecall/mod.rs vm/src/chips/chips/riscv_cpu/ecall/columns.rs vm/src/chips/chips/riscv_cpu/ecall/constraints.rs vm/src/chips/chips/riscv_cpu/ecall/traces.rs vm/src/chips/chips/riscv_cpu/event.rs vm/src/chips/chips/riscv_cpu/instruction/mod.rs vm/src/chips/chips/riscv_cpu/instruction/columns.rs vm/src/chips/chips/riscv_cpu/jump/mod.rs vm/src/chips/chips/riscv_cpu/jump/columns.rs vm/src/chips/chips/riscv_cpu/jump/constraints.rs vm/src/chips/chips/riscv_cpu/jump/traces.rs vm/src/chips/chips/riscv_cpu/opcode_selector/mod.rs vm/src/chips/chips/riscv_cpu/opcode_selector/columns.rs vm/src/chips/chips/riscv_cpu/opcode_specific/mod.rs vm/src/chips/chips/riscv_cpu/opcode_specific/columns.rs vm/src/chips/chips/riscv_cpu/public_values/mod.rs vm/src/chips/chips/riscv_cpu/public_values/constraints.rs vm/src/chips/chips/riscv_cpu/register/mod.rs vm/src/chips/chips/riscv_cpu/register/constraints.rs vm/src/chips/chips/riscv_cpu/traces.rs vm/src/chips/chips/riscv_cpu/utils.rs vm/src/chips/chips/riscv_global/mod.rs vm/src/chips/chips/riscv_global/columns.rs vm/src/chips/chips/riscv_global/constraints.rs vm/src/chips/chips/riscv_global/event.rs vm/src/chips/chips/riscv_global/traces.rs vm/src/chips/chips/riscv_memory/mod.rs vm/src/chips/chips/riscv_memory/event.rs vm/src/chips/chips/riscv_memory/initialize_finalize/mod.rs vm/src/chips/chips/riscv_memory/initialize_finalize/columns.rs vm/src/chips/chips/riscv_memory/initialize_finalize/constraints.rs vm/src/chips/chips/riscv_memory/initialize_finalize/traces.rs vm/src/chips/chips/riscv_memory/local/mod.rs vm/src/chips/chips/riscv_memory/local/columns.rs vm/src/chips/chips/riscv_memory/local/constraints.rs vm/src/chips/chips/riscv_memory/local/traces.rs vm/src/chips/chips/riscv_memory/read_write/mod.rs vm/src/chips/chips/riscv_memory/read_write/columns.rs vm/src/chips/chips/riscv_memory/read_write/constraints.rs vm/src/chips/chips/riscv_memory/read_write/traces.rs vm/src/chips/chips/riscv_poseidon2/mod.rs vm/src/chips/chips/riscv_poseidon2/constraints.rs vm/src/chips/chips/riscv_poseidon2/event.rs vm/src/chips/chips/riscv_poseidon2/traces.rs vm/src/chips/chips/riscv_program/mod.rs vm/src/chips/chips/riscv_program/columns.rs vm/src/chips/chips/riscv_program/constraints.rs vm/src/chips/chips/riscv_program/traces.rs vm/src/chips/chips/toys/mod.rs vm/src/chips/chips/toys/lookup_toy.rs vm/src/chips/chips/toys/toy.rs vm/src/chips/chips/events.rs vm/src/chips/chips/alu_base/mod.rs vm/src/chips/chips/alu_base/columns.rs vm/src/chips/chips/alu_base/constraints.rs vm/src/chips/chips/alu_base/traces.rs vm/src/chips/chips/alu_ext/mod.rs vm/src/chips/chips/alu_ext/columns.rs vm/src/chips/chips/alu_ext/constraints.rs vm/src/chips/chips/alu_ext/traces.rs vm/src/chips/chips/batch_fri/mod.rs vm/src/chips/chips/batch_fri/columns.rs vm/src/chips/chips/batch_fri/constraints.rs vm/src/chips/chips/batch_fri/traces.rs vm/src/chips/chips/exp_reverse_bits/mod.rs vm/src/chips/chips/exp_reverse_bits/columns.rs vm/src/chips/chips/exp_reverse_bits/constraints.rs vm/src/chips/chips/exp_reverse_bits/traces.rs vm/src/chips/chips/poseidon2/mod.rs vm/src/chips/chips/poseidon2/constraints.rs vm/src/chips/chips/poseidon2/traces.rs vm/src/chips/chips/public_values/mod.rs vm/src/chips/chips/public_values/columns.rs vm/src/chips/chips/public_values/constraints.rs vm/src/chips/chips/public_values/traces.rs vm/src/chips/chips/recursion_memory/mod.rs vm/src/chips/chips/recursion_memory/constant/mod.rs vm/src/chips/chips/recursion_memory/constant/columns.rs vm/src/chips/chips/recursion_memory/constant/constraints.rs vm/src/chips/chips/recursion_memory/constant/traces.rs vm/src/chips/chips/recursion_memory/variable/mod.rs vm/src/chips/chips/recursion_memory/variable/columns.rs vm/src/chips/chips/recursion_memory/variable/constraints.rs vm/src/chips/chips/recursion_memory/variable/traces.rs vm/src/chips/chips/select/mod.rs vm/src/chips/chips/select/columns.rs vm/src/chips/chips/select/constraints.rs vm/src/chips/chips/select/trace.rs vm/src/chips/chips/syscall/mod.rs vm/src/chips/chips/syscall/columns.rs vm/src/chips/chips/syscall/constraints.rs vm/src/chips/chips/syscall/traces.rs vm/src/chips/gadgets/mod.rs vm/src/chips/gadgets/add.rs vm/src/chips/gadgets/add4.rs vm/src/chips/gadgets/add5.rs vm/src/chips/gadgets/and.rs vm/src/chips/gadgets/curves/mod.rs vm/src/chips/gadgets/curves/edwards/mod.rs vm/src/chips/gadgets/curves/edwards/ed25519.rs vm/src/chips/gadgets/curves/scalar_mul.rs vm/src/chips/gadgets/curves/weierstrass/mod.rs vm/src/chips/gadgets/curves/weierstrass/bls381.rs vm/src/chips/gadgets/curves/weierstrass/bn254.rs vm/src/chips/gadgets/curves/weierstrass/secp256k1.rs vm/src/chips/gadgets/field/mod.rs vm/src/chips/gadgets/field/bls381.rs vm/src/chips/gadgets/field/bn254.rs vm/src/chips/gadgets/field/field_den.rs vm/src/chips/gadgets/field/field_inner_product.rs vm/src/chips/gadgets/field/field_lt.rs vm/src/chips/gadgets/field/field_op.rs vm/src/chips/gadgets/field/field_sqrt.rs vm/src/chips/gadgets/field/secp256k1.rs vm/src/chips/gadgets/field/utils.rs vm/src/chips/gadgets/field_range_check/mod.rs vm/src/chips/gadgets/field_range_check/bit_decomposition.rs vm/src/chips/gadgets/field_range_check/word_range.rs vm/src/chips/gadgets/fixed_rotate_right.rs vm/src/chips/gadgets/fixed_shift_right.rs vm/src/chips/gadgets/global_accumulation.rs vm/src/chips/gadgets/global_interaction.rs vm/src/chips/gadgets/is_equal_word.rs vm/src/chips/gadgets/is_zero.rs vm/src/chips/gadgets/is_zero_word.rs vm/src/chips/gadgets/lt.rs vm/src/chips/gadgets/not.rs vm/src/chips/gadgets/poseidon2/mod.rs vm/src/chips/gadgets/poseidon2/columns.rs vm/src/chips/gadgets/poseidon2/constants.rs vm/src/chips/gadgets/poseidon2/constraints.rs vm/src/chips/gadgets/poseidon2/traces.rs vm/src/chips/gadgets/poseidon2/utils.rs vm/src/chips/gadgets/uint256/mod.rs vm/src/chips/gadgets/utils/mod.rs vm/src/chips/gadgets/utils/conversions.rs vm/src/chips/gadgets/utils/field_params.rs vm/src/chips/gadgets/utils/limbs.rs vm/src/chips/gadgets/utils/polynomial.rs vm/src/chips/gadgets/xor.rs vm/src/chips/precompiles/mod.rs vm/src/chips/precompiles/edwards/mod.rs vm/src/chips/precompiles/edwards/ed_add.rs vm/src/chips/precompiles/edwards/ed_decompress.rs vm/src/chips/precompiles/fptower/mod.rs vm/src/chips/precompiles/fptower/fp.rs vm/src/chips/precompiles/fptower/fp2_addsub.rs vm/src/chips/precompiles/fptower/fp2_mul.rs vm/src/chips/precompiles/keccak256/mod.rs vm/src/chips/precompiles/keccak256/columns.rs vm/src/chips/precompiles/keccak256/constraint.rs vm/src/chips/precompiles/keccak256/traces.rs vm/src/chips/precompiles/poseidon2/mod.rs vm/src/chips/precompiles/poseidon2/columns.rs vm/src/chips/precompiles/poseidon2/constraints.rs vm/src/chips/precompiles/poseidon2/traces.rs vm/src/chips/precompiles/sha256/mod.rs vm/src/chips/precompiles/sha256/compress/mod.rs vm/src/chips/precompiles/sha256/compress/columns.rs vm/src/chips/precompiles/sha256/compress/constraints.rs vm/src/chips/precompiles/sha256/compress/trace.rs vm/src/chips/precompiles/sha256/extend/mod.rs vm/src/chips/precompiles/sha256/extend/columns.rs vm/src/chips/precompiles/sha256/extend/constraints.rs vm/src/chips/precompiles/sha256/extend/flags.rs vm/src/chips/precompiles/sha256/extend/trace.rs vm/src/chips/precompiles/uint256/mod.rs vm/src/chips/precompiles/uint256/columns.rs vm/src/chips/precompiles/uint256/constraints.rs vm/src/chips/precompiles/uint256/traces.rs vm/src/chips/precompiles/weierstrass/mod.rs vm/src/chips/precompiles/weierstrass/weierstrass_add.rs vm/src/chips/precompiles/weierstrass/weierstrass_decompress.rs vm/src/chips/precompiles/weierstrass/weierstrass_double.rs vm/src/chips/trace.rs vm/src/chips/utils.rs vm/src/compiler/mod.rs vm/src/compiler/program.rs vm/src/compiler/recursion/mod.rs vm/src/compiler/recursion/circuit/mod.rs vm/src/compiler/recursion/circuit/builder.rs vm/src/compiler/recursion/circuit/challenger.rs vm/src/compiler/recursion/circuit/config.rs vm/src/compiler/recursion/circuit/constraints.rs vm/src/compiler/recursion/circuit/domain.rs vm/src/compiler/recursion/circuit/fri.rs vm/src/compiler/recursion/circuit/hash.rs vm/src/compiler/recursion/circuit/merkle_tree.rs vm/src/compiler/recursion/circuit/stark.rs vm/src/compiler/recursion/circuit/types.rs vm/src/compiler/recursion/circuit/utils.rs vm/src/compiler/recursion/circuit/witness/mod.rs vm/src/compiler/recursion/circuit/witness/embed.rs vm/src/compiler/recursion/circuit/witness/stark.rs vm/src/compiler/recursion/circuit/witness/witnessable.rs vm/src/compiler/recursion/constraints/mod.rs vm/src/compiler/recursion/constraints/opcodes.rs vm/src/compiler/recursion/instruction.rs vm/src/compiler/recursion/ir/mod.rs vm/src/compiler/recursion/ir/arithmetic.rs vm/src/compiler/recursion/ir/bits.rs vm/src/compiler/recursion/ir/block.rs vm/src/compiler/recursion/ir/builder.rs vm/src/compiler/recursion/ir/collections.rs vm/src/compiler/recursion/ir/compiler.rs vm/src/compiler/recursion/ir/instructions.rs vm/src/compiler/recursion/ir/ptr.rs vm/src/compiler/recursion/ir/symbolic.rs vm/src/compiler/recursion/ir/types.rs vm/src/compiler/recursion/ir/utils.rs vm/src/compiler/recursion/ir/var.rs vm/src/compiler/recursion/program.rs vm/src/compiler/recursion/types.rs vm/src/compiler/riscv/mod.rs vm/src/compiler/riscv/compiler.rs vm/src/compiler/riscv/disassembler/mod.rs vm/src/compiler/riscv/disassembler/elf.rs vm/src/compiler/riscv/disassembler/rrs.rs vm/src/compiler/riscv/instruction.rs vm/src/compiler/riscv/opcode.rs vm/src/compiler/riscv/program.rs vm/src/compiler/riscv/register.rs vm/src/compiler/word.rs vm/src/configs/mod.rs vm/src/configs/config.rs vm/src/configs/field_config/mod.rs vm/src/configs/field_config/bb_bn254.rs vm/src/configs/field_config/bb_simple.rs vm/src/configs/field_config/kb_bn254.rs vm/src/configs/field_config/kb_simple.rs vm/src/configs/stark_config/mod.rs vm/src/configs/stark_config/bb_bn254_poseidon2.rs vm/src/configs/stark_config/bb_poseidon2.rs vm/src/configs/stark_config/kb_bn254_poseidon2.rs vm/src/configs/stark_config/kb_poseidon2.rs vm/src/configs/stark_config/m31_poseidon2.rs vm/src/emulator/mod.rs vm/src/emulator/emulator.rs vm/src/emulator/opts.rs vm/src/emulator/record.rs vm/src/emulator/recursion/mod.rs vm/src/emulator/recursion/emulator/mod.rs vm/src/emulator/recursion/emulator/memory.rs vm/src/emulator/recursion/emulator/opcode.rs vm/src/emulator/recursion/public_values.rs vm/src/emulator/recursion/record.rs vm/src/emulator/riscv/mod.rs vm/src/emulator/riscv/emulator/mod.rs vm/src/emulator/riscv/emulator/error.rs vm/src/emulator/riscv/emulator/instruction.rs vm/src/emulator/riscv/emulator/instruction_simple.rs vm/src/emulator/riscv/emulator/mode.rs vm/src/emulator/riscv/emulator/unconstrained.rs vm/src/emulator/riscv/emulator/util.rs vm/src/emulator/riscv/hook/mod.rs vm/src/emulator/riscv/hook/ecrecover.rs vm/src/emulator/riscv/hook/ed_decompress.rs vm/src/emulator/riscv/memory.rs vm/src/emulator/riscv/public_values.rs vm/src/emulator/riscv/record.rs vm/src/emulator/riscv/state.rs vm/src/emulator/riscv/syscalls/mod.rs vm/src/emulator/riscv/syscalls/code.rs vm/src/emulator/riscv/syscalls/commit.rs vm/src/emulator/riscv/syscalls/deferred.rs vm/src/emulator/riscv/syscalls/halt.rs vm/src/emulator/riscv/syscalls/hint.rs vm/src/emulator/riscv/syscalls/precompiles/mod.rs vm/src/emulator/riscv/syscalls/precompiles/ec/mod.rs vm/src/emulator/riscv/syscalls/precompiles/ec/event.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/mod.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/add.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/decompress.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/event.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/mod.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/event.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_addsub.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_mul.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/event.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/permute.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/mod.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/event.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/permute.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/compress.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/event.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/extend.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/event.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/syscall.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/mod.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/add.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/decompress.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/double.rs vm/src/emulator/riscv/syscalls/syscall_context.rs vm/src/emulator/riscv/syscalls/unconstrained.rs vm/src/emulator/riscv/syscalls/verify.rs vm/src/emulator/riscv/syscalls/write.rs vm/src/emulator/stdin.rs vm/src/instances/mod.rs vm/src/instances/chiptype/mod.rs vm/src/instances/chiptype/chiptype_macros/mod.rs vm/src/instances/chiptype/chiptype_macros/define_chip_type.rs vm/src/instances/chiptype/chiptype_macros/enum_chip_type.rs vm/src/instances/chiptype/chiptype_macros/impl_air.rs vm/src/instances/chiptype/chiptype_macros/impl_base_air.rs vm/src/instances/chiptype/chiptype_macros/impl_chip_behavior.rs vm/src/instances/chiptype/recursion_chiptype.rs vm/src/instances/chiptype/riscv_chiptype.rs vm/src/instances/compiler/mod.rs vm/src/instances/compiler/onchain_circuit/mod.rs vm/src/instances/compiler/onchain_circuit/gnark/mod.rs vm/src/instances/compiler/onchain_circuit/gnark/builder.rs vm/src/instances/compiler/onchain_circuit/gnark/witness.rs vm/src/instances/compiler/onchain_circuit/stdin.rs vm/src/instances/compiler/onchain_circuit/utils.rs vm/src/instances/compiler/recursion_circuit/mod.rs vm/src/instances/compiler/recursion_circuit/combine/mod.rs vm/src/instances/compiler/recursion_circuit/combine/builder.rs vm/src/instances/compiler/recursion_circuit/compress/mod.rs vm/src/instances/compiler/recursion_circuit/compress/builder.rs vm/src/instances/compiler/recursion_circuit/embed/mod.rs vm/src/instances/compiler/recursion_circuit/embed/builder.rs vm/src/instances/compiler/recursion_circuit/stdin.rs vm/src/instances/compiler/riscv_circuit/mod.rs vm/src/instances/compiler/riscv_circuit/convert/mod.rs vm/src/instances/compiler/riscv_circuit/convert/builder.rs vm/src/instances/compiler/riscv_circuit/deferred/mod.rs vm/src/instances/compiler/riscv_circuit/deferred/builder.rs vm/src/instances/compiler/riscv_circuit/stdin.rs vm/src/instances/compiler/shapes/mod.rs vm/src/instances/compiler/shapes/recursion_shape.rs vm/src/instances/compiler/shapes/riscv_shape.rs vm/src/instances/compiler/simple_circuit/mod.rs vm/src/instances/compiler/simple_circuit/builder.rs vm/src/instances/compiler/simple_circuit/stdin.rs vm/src/instances/compiler/vk_merkle/mod.rs vm/src/instances/compiler/vk_merkle/builder.rs vm/src/instances/compiler/vk_merkle/stdin.rs vm/src/instances/compiler/witness.rs vm/src/instances/configs/mod.rs vm/src/instances/configs/embed_bb_bn254_poseidon2.rs vm/src/instances/configs/embed_kb_bn254_poseidon2.rs vm/src/instances/configs/recur_bb_poseidon2.rs vm/src/instances/configs/recur_kb_poseidon2.rs vm/src/instances/configs/riscv_bb_poseidon2.rs vm/src/instances/configs/riscv_kb_poseidon2.rs vm/src/instances/configs/riscv_m31_poseidon2.rs vm/src/instances/machine/mod.rs vm/src/instances/machine/combine.rs vm/src/instances/machine/compress.rs vm/src/instances/machine/convert.rs vm/src/instances/machine/deferred.rs vm/src/instances/machine/embed.rs vm/src/instances/machine/riscv.rs vm/src/instances/machine/simple.rs vm/src/iter/mod.rs vm/src/iter/rayon/mod.rs vm/src/iter/rayon/impls.rs vm/src/machine/mod.rs vm/src/machine/builder/mod.rs vm/src/machine/builder/base.rs vm/src/machine/builder/extension.rs vm/src/machine/builder/lookup.rs vm/src/machine/builder/permutation.rs vm/src/machine/builder/public_values.rs vm/src/machine/builder/range_check.rs vm/src/machine/builder/recursion.rs vm/src/machine/builder/riscv_memory.rs vm/src/machine/builder/scoped.rs vm/src/machine/builder/septic.rs vm/src/machine/builder/sub_builder.rs vm/src/machine/builder/word.rs vm/src/machine/chip.rs vm/src/machine/debug/mod.rs vm/src/machine/debug/constraints.rs vm/src/machine/debug/lookups.rs vm/src/machine/extension.rs vm/src/machine/field.rs vm/src/machine/folder.rs vm/src/machine/keys.rs vm/src/machine/logger.rs vm/src/machine/lookup.rs vm/src/machine/machine.rs vm/src/machine/permutation.rs vm/src/machine/proof.rs vm/src/machine/prover.rs vm/src/machine/septic/mod.rs vm/src/machine/septic/curve.rs vm/src/machine/septic/digest.rs vm/src/machine/septic/extension.rs vm/src/machine/septic/fields/mod.rs vm/src/machine/septic/fields/babybear.rs vm/src/machine/septic/fields/dummy.rs vm/src/machine/septic/fields/koalabear.rs vm/src/machine/septic/fields/mersenne31.rs vm/src/machine/utils.rs vm/src/machine/verifier.rs vm/src/machine/witness.rs vm/src/primitives/mod.rs vm/src/primitives/consts.rs vm/src/primitives/poseidon2/mod.rs vm/src/primitives/poseidon2/babybear.rs vm/src/primitives/poseidon2/koalabear.rs vm/src/primitives/poseidon2/mersenne31.rs vm/src/proverchain/mod.rs vm/src/proverchain/combine.rs vm/src/proverchain/compress.rs vm/src/proverchain/convert.rs vm/src/proverchain/embed.rs vm/src/proverchain/riscv.rs vm/src/proverchain/deferred.rs vm/src/thread/mod.rs vm/src/thread/channel.rs vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_bb.bin vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_kb.bin

/home/<USER>/2025-08-pico-x15-eth/pico/target/debug/deps/libpico_vm-0cbd12e0bc32786e.rmeta: vm/src/lib.rs vm/src/chips/mod.rs vm/src/chips/chips/mod.rs vm/src/chips/chips/alu/mod.rs vm/src/chips/chips/alu/add_sub/mod.rs vm/src/chips/chips/alu/add_sub/columns.rs vm/src/chips/chips/alu/add_sub/constraints.rs vm/src/chips/chips/alu/add_sub/traces.rs vm/src/chips/chips/alu/bitwise/mod.rs vm/src/chips/chips/alu/bitwise/columns.rs vm/src/chips/chips/alu/bitwise/constraints.rs vm/src/chips/chips/alu/bitwise/traces.rs vm/src/chips/chips/alu/divrem/mod.rs vm/src/chips/chips/alu/divrem/columns.rs vm/src/chips/chips/alu/divrem/constraints.rs vm/src/chips/chips/alu/divrem/traces.rs vm/src/chips/chips/alu/divrem/utils.rs vm/src/chips/chips/alu/event.rs vm/src/chips/chips/alu/lt/mod.rs vm/src/chips/chips/alu/lt/columns.rs vm/src/chips/chips/alu/lt/constraints.rs vm/src/chips/chips/alu/lt/traces.rs vm/src/chips/chips/alu/mul/mod.rs vm/src/chips/chips/alu/mul/columns.rs vm/src/chips/chips/alu/mul/constraints.rs vm/src/chips/chips/alu/mul/traces.rs vm/src/chips/chips/alu/sll/mod.rs vm/src/chips/chips/alu/sll/columns.rs vm/src/chips/chips/alu/sll/constraints.rs vm/src/chips/chips/alu/sll/traces.rs vm/src/chips/chips/alu/sr/mod.rs vm/src/chips/chips/alu/sr/columns.rs vm/src/chips/chips/alu/sr/constraints.rs vm/src/chips/chips/alu/sr/traces.rs vm/src/chips/chips/byte/mod.rs vm/src/chips/chips/byte/columns.rs vm/src/chips/chips/byte/constraints.rs vm/src/chips/chips/byte/event.rs vm/src/chips/chips/byte/traces.rs vm/src/chips/chips/byte/utils.rs vm/src/chips/chips/riscv_cpu/mod.rs vm/src/chips/chips/riscv_cpu/auipc/mod.rs vm/src/chips/chips/riscv_cpu/auipc/columns.rs vm/src/chips/chips/riscv_cpu/auipc/constraints.rs vm/src/chips/chips/riscv_cpu/auipc/traces.rs vm/src/chips/chips/riscv_cpu/branch/mod.rs vm/src/chips/chips/riscv_cpu/branch/columns.rs vm/src/chips/chips/riscv_cpu/branch/constraints.rs vm/src/chips/chips/riscv_cpu/branch/traces.rs vm/src/chips/chips/riscv_cpu/chunk_clk/mod.rs vm/src/chips/chips/riscv_cpu/chunk_clk/constraints.rs vm/src/chips/chips/riscv_cpu/chunk_clk/traces.rs vm/src/chips/chips/riscv_cpu/columns.rs vm/src/chips/chips/riscv_cpu/constraints.rs vm/src/chips/chips/riscv_cpu/ecall/mod.rs vm/src/chips/chips/riscv_cpu/ecall/columns.rs vm/src/chips/chips/riscv_cpu/ecall/constraints.rs vm/src/chips/chips/riscv_cpu/ecall/traces.rs vm/src/chips/chips/riscv_cpu/event.rs vm/src/chips/chips/riscv_cpu/instruction/mod.rs vm/src/chips/chips/riscv_cpu/instruction/columns.rs vm/src/chips/chips/riscv_cpu/jump/mod.rs vm/src/chips/chips/riscv_cpu/jump/columns.rs vm/src/chips/chips/riscv_cpu/jump/constraints.rs vm/src/chips/chips/riscv_cpu/jump/traces.rs vm/src/chips/chips/riscv_cpu/opcode_selector/mod.rs vm/src/chips/chips/riscv_cpu/opcode_selector/columns.rs vm/src/chips/chips/riscv_cpu/opcode_specific/mod.rs vm/src/chips/chips/riscv_cpu/opcode_specific/columns.rs vm/src/chips/chips/riscv_cpu/public_values/mod.rs vm/src/chips/chips/riscv_cpu/public_values/constraints.rs vm/src/chips/chips/riscv_cpu/register/mod.rs vm/src/chips/chips/riscv_cpu/register/constraints.rs vm/src/chips/chips/riscv_cpu/traces.rs vm/src/chips/chips/riscv_cpu/utils.rs vm/src/chips/chips/riscv_global/mod.rs vm/src/chips/chips/riscv_global/columns.rs vm/src/chips/chips/riscv_global/constraints.rs vm/src/chips/chips/riscv_global/event.rs vm/src/chips/chips/riscv_global/traces.rs vm/src/chips/chips/riscv_memory/mod.rs vm/src/chips/chips/riscv_memory/event.rs vm/src/chips/chips/riscv_memory/initialize_finalize/mod.rs vm/src/chips/chips/riscv_memory/initialize_finalize/columns.rs vm/src/chips/chips/riscv_memory/initialize_finalize/constraints.rs vm/src/chips/chips/riscv_memory/initialize_finalize/traces.rs vm/src/chips/chips/riscv_memory/local/mod.rs vm/src/chips/chips/riscv_memory/local/columns.rs vm/src/chips/chips/riscv_memory/local/constraints.rs vm/src/chips/chips/riscv_memory/local/traces.rs vm/src/chips/chips/riscv_memory/read_write/mod.rs vm/src/chips/chips/riscv_memory/read_write/columns.rs vm/src/chips/chips/riscv_memory/read_write/constraints.rs vm/src/chips/chips/riscv_memory/read_write/traces.rs vm/src/chips/chips/riscv_poseidon2/mod.rs vm/src/chips/chips/riscv_poseidon2/constraints.rs vm/src/chips/chips/riscv_poseidon2/event.rs vm/src/chips/chips/riscv_poseidon2/traces.rs vm/src/chips/chips/riscv_program/mod.rs vm/src/chips/chips/riscv_program/columns.rs vm/src/chips/chips/riscv_program/constraints.rs vm/src/chips/chips/riscv_program/traces.rs vm/src/chips/chips/toys/mod.rs vm/src/chips/chips/toys/lookup_toy.rs vm/src/chips/chips/toys/toy.rs vm/src/chips/chips/events.rs vm/src/chips/chips/alu_base/mod.rs vm/src/chips/chips/alu_base/columns.rs vm/src/chips/chips/alu_base/constraints.rs vm/src/chips/chips/alu_base/traces.rs vm/src/chips/chips/alu_ext/mod.rs vm/src/chips/chips/alu_ext/columns.rs vm/src/chips/chips/alu_ext/constraints.rs vm/src/chips/chips/alu_ext/traces.rs vm/src/chips/chips/batch_fri/mod.rs vm/src/chips/chips/batch_fri/columns.rs vm/src/chips/chips/batch_fri/constraints.rs vm/src/chips/chips/batch_fri/traces.rs vm/src/chips/chips/exp_reverse_bits/mod.rs vm/src/chips/chips/exp_reverse_bits/columns.rs vm/src/chips/chips/exp_reverse_bits/constraints.rs vm/src/chips/chips/exp_reverse_bits/traces.rs vm/src/chips/chips/poseidon2/mod.rs vm/src/chips/chips/poseidon2/constraints.rs vm/src/chips/chips/poseidon2/traces.rs vm/src/chips/chips/public_values/mod.rs vm/src/chips/chips/public_values/columns.rs vm/src/chips/chips/public_values/constraints.rs vm/src/chips/chips/public_values/traces.rs vm/src/chips/chips/recursion_memory/mod.rs vm/src/chips/chips/recursion_memory/constant/mod.rs vm/src/chips/chips/recursion_memory/constant/columns.rs vm/src/chips/chips/recursion_memory/constant/constraints.rs vm/src/chips/chips/recursion_memory/constant/traces.rs vm/src/chips/chips/recursion_memory/variable/mod.rs vm/src/chips/chips/recursion_memory/variable/columns.rs vm/src/chips/chips/recursion_memory/variable/constraints.rs vm/src/chips/chips/recursion_memory/variable/traces.rs vm/src/chips/chips/select/mod.rs vm/src/chips/chips/select/columns.rs vm/src/chips/chips/select/constraints.rs vm/src/chips/chips/select/trace.rs vm/src/chips/chips/syscall/mod.rs vm/src/chips/chips/syscall/columns.rs vm/src/chips/chips/syscall/constraints.rs vm/src/chips/chips/syscall/traces.rs vm/src/chips/gadgets/mod.rs vm/src/chips/gadgets/add.rs vm/src/chips/gadgets/add4.rs vm/src/chips/gadgets/add5.rs vm/src/chips/gadgets/and.rs vm/src/chips/gadgets/curves/mod.rs vm/src/chips/gadgets/curves/edwards/mod.rs vm/src/chips/gadgets/curves/edwards/ed25519.rs vm/src/chips/gadgets/curves/scalar_mul.rs vm/src/chips/gadgets/curves/weierstrass/mod.rs vm/src/chips/gadgets/curves/weierstrass/bls381.rs vm/src/chips/gadgets/curves/weierstrass/bn254.rs vm/src/chips/gadgets/curves/weierstrass/secp256k1.rs vm/src/chips/gadgets/field/mod.rs vm/src/chips/gadgets/field/bls381.rs vm/src/chips/gadgets/field/bn254.rs vm/src/chips/gadgets/field/field_den.rs vm/src/chips/gadgets/field/field_inner_product.rs vm/src/chips/gadgets/field/field_lt.rs vm/src/chips/gadgets/field/field_op.rs vm/src/chips/gadgets/field/field_sqrt.rs vm/src/chips/gadgets/field/secp256k1.rs vm/src/chips/gadgets/field/utils.rs vm/src/chips/gadgets/field_range_check/mod.rs vm/src/chips/gadgets/field_range_check/bit_decomposition.rs vm/src/chips/gadgets/field_range_check/word_range.rs vm/src/chips/gadgets/fixed_rotate_right.rs vm/src/chips/gadgets/fixed_shift_right.rs vm/src/chips/gadgets/global_accumulation.rs vm/src/chips/gadgets/global_interaction.rs vm/src/chips/gadgets/is_equal_word.rs vm/src/chips/gadgets/is_zero.rs vm/src/chips/gadgets/is_zero_word.rs vm/src/chips/gadgets/lt.rs vm/src/chips/gadgets/not.rs vm/src/chips/gadgets/poseidon2/mod.rs vm/src/chips/gadgets/poseidon2/columns.rs vm/src/chips/gadgets/poseidon2/constants.rs vm/src/chips/gadgets/poseidon2/constraints.rs vm/src/chips/gadgets/poseidon2/traces.rs vm/src/chips/gadgets/poseidon2/utils.rs vm/src/chips/gadgets/uint256/mod.rs vm/src/chips/gadgets/utils/mod.rs vm/src/chips/gadgets/utils/conversions.rs vm/src/chips/gadgets/utils/field_params.rs vm/src/chips/gadgets/utils/limbs.rs vm/src/chips/gadgets/utils/polynomial.rs vm/src/chips/gadgets/xor.rs vm/src/chips/precompiles/mod.rs vm/src/chips/precompiles/edwards/mod.rs vm/src/chips/precompiles/edwards/ed_add.rs vm/src/chips/precompiles/edwards/ed_decompress.rs vm/src/chips/precompiles/fptower/mod.rs vm/src/chips/precompiles/fptower/fp.rs vm/src/chips/precompiles/fptower/fp2_addsub.rs vm/src/chips/precompiles/fptower/fp2_mul.rs vm/src/chips/precompiles/keccak256/mod.rs vm/src/chips/precompiles/keccak256/columns.rs vm/src/chips/precompiles/keccak256/constraint.rs vm/src/chips/precompiles/keccak256/traces.rs vm/src/chips/precompiles/poseidon2/mod.rs vm/src/chips/precompiles/poseidon2/columns.rs vm/src/chips/precompiles/poseidon2/constraints.rs vm/src/chips/precompiles/poseidon2/traces.rs vm/src/chips/precompiles/sha256/mod.rs vm/src/chips/precompiles/sha256/compress/mod.rs vm/src/chips/precompiles/sha256/compress/columns.rs vm/src/chips/precompiles/sha256/compress/constraints.rs vm/src/chips/precompiles/sha256/compress/trace.rs vm/src/chips/precompiles/sha256/extend/mod.rs vm/src/chips/precompiles/sha256/extend/columns.rs vm/src/chips/precompiles/sha256/extend/constraints.rs vm/src/chips/precompiles/sha256/extend/flags.rs vm/src/chips/precompiles/sha256/extend/trace.rs vm/src/chips/precompiles/uint256/mod.rs vm/src/chips/precompiles/uint256/columns.rs vm/src/chips/precompiles/uint256/constraints.rs vm/src/chips/precompiles/uint256/traces.rs vm/src/chips/precompiles/weierstrass/mod.rs vm/src/chips/precompiles/weierstrass/weierstrass_add.rs vm/src/chips/precompiles/weierstrass/weierstrass_decompress.rs vm/src/chips/precompiles/weierstrass/weierstrass_double.rs vm/src/chips/trace.rs vm/src/chips/utils.rs vm/src/compiler/mod.rs vm/src/compiler/program.rs vm/src/compiler/recursion/mod.rs vm/src/compiler/recursion/circuit/mod.rs vm/src/compiler/recursion/circuit/builder.rs vm/src/compiler/recursion/circuit/challenger.rs vm/src/compiler/recursion/circuit/config.rs vm/src/compiler/recursion/circuit/constraints.rs vm/src/compiler/recursion/circuit/domain.rs vm/src/compiler/recursion/circuit/fri.rs vm/src/compiler/recursion/circuit/hash.rs vm/src/compiler/recursion/circuit/merkle_tree.rs vm/src/compiler/recursion/circuit/stark.rs vm/src/compiler/recursion/circuit/types.rs vm/src/compiler/recursion/circuit/utils.rs vm/src/compiler/recursion/circuit/witness/mod.rs vm/src/compiler/recursion/circuit/witness/embed.rs vm/src/compiler/recursion/circuit/witness/stark.rs vm/src/compiler/recursion/circuit/witness/witnessable.rs vm/src/compiler/recursion/constraints/mod.rs vm/src/compiler/recursion/constraints/opcodes.rs vm/src/compiler/recursion/instruction.rs vm/src/compiler/recursion/ir/mod.rs vm/src/compiler/recursion/ir/arithmetic.rs vm/src/compiler/recursion/ir/bits.rs vm/src/compiler/recursion/ir/block.rs vm/src/compiler/recursion/ir/builder.rs vm/src/compiler/recursion/ir/collections.rs vm/src/compiler/recursion/ir/compiler.rs vm/src/compiler/recursion/ir/instructions.rs vm/src/compiler/recursion/ir/ptr.rs vm/src/compiler/recursion/ir/symbolic.rs vm/src/compiler/recursion/ir/types.rs vm/src/compiler/recursion/ir/utils.rs vm/src/compiler/recursion/ir/var.rs vm/src/compiler/recursion/program.rs vm/src/compiler/recursion/types.rs vm/src/compiler/riscv/mod.rs vm/src/compiler/riscv/compiler.rs vm/src/compiler/riscv/disassembler/mod.rs vm/src/compiler/riscv/disassembler/elf.rs vm/src/compiler/riscv/disassembler/rrs.rs vm/src/compiler/riscv/instruction.rs vm/src/compiler/riscv/opcode.rs vm/src/compiler/riscv/program.rs vm/src/compiler/riscv/register.rs vm/src/compiler/word.rs vm/src/configs/mod.rs vm/src/configs/config.rs vm/src/configs/field_config/mod.rs vm/src/configs/field_config/bb_bn254.rs vm/src/configs/field_config/bb_simple.rs vm/src/configs/field_config/kb_bn254.rs vm/src/configs/field_config/kb_simple.rs vm/src/configs/stark_config/mod.rs vm/src/configs/stark_config/bb_bn254_poseidon2.rs vm/src/configs/stark_config/bb_poseidon2.rs vm/src/configs/stark_config/kb_bn254_poseidon2.rs vm/src/configs/stark_config/kb_poseidon2.rs vm/src/configs/stark_config/m31_poseidon2.rs vm/src/emulator/mod.rs vm/src/emulator/emulator.rs vm/src/emulator/opts.rs vm/src/emulator/record.rs vm/src/emulator/recursion/mod.rs vm/src/emulator/recursion/emulator/mod.rs vm/src/emulator/recursion/emulator/memory.rs vm/src/emulator/recursion/emulator/opcode.rs vm/src/emulator/recursion/public_values.rs vm/src/emulator/recursion/record.rs vm/src/emulator/riscv/mod.rs vm/src/emulator/riscv/emulator/mod.rs vm/src/emulator/riscv/emulator/error.rs vm/src/emulator/riscv/emulator/instruction.rs vm/src/emulator/riscv/emulator/instruction_simple.rs vm/src/emulator/riscv/emulator/mode.rs vm/src/emulator/riscv/emulator/unconstrained.rs vm/src/emulator/riscv/emulator/util.rs vm/src/emulator/riscv/hook/mod.rs vm/src/emulator/riscv/hook/ecrecover.rs vm/src/emulator/riscv/hook/ed_decompress.rs vm/src/emulator/riscv/memory.rs vm/src/emulator/riscv/public_values.rs vm/src/emulator/riscv/record.rs vm/src/emulator/riscv/state.rs vm/src/emulator/riscv/syscalls/mod.rs vm/src/emulator/riscv/syscalls/code.rs vm/src/emulator/riscv/syscalls/commit.rs vm/src/emulator/riscv/syscalls/deferred.rs vm/src/emulator/riscv/syscalls/halt.rs vm/src/emulator/riscv/syscalls/hint.rs vm/src/emulator/riscv/syscalls/precompiles/mod.rs vm/src/emulator/riscv/syscalls/precompiles/ec/mod.rs vm/src/emulator/riscv/syscalls/precompiles/ec/event.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/mod.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/add.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/decompress.rs vm/src/emulator/riscv/syscalls/precompiles/edwards/event.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/mod.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/event.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_addsub.rs vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_mul.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/event.rs vm/src/emulator/riscv/syscalls/precompiles/keccak256/permute.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/mod.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/event.rs vm/src/emulator/riscv/syscalls/precompiles/poseidon2/permute.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/compress.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/event.rs vm/src/emulator/riscv/syscalls/precompiles/sha256/extend.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/mod.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/event.rs vm/src/emulator/riscv/syscalls/precompiles/uint256/syscall.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/mod.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/add.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/decompress.rs vm/src/emulator/riscv/syscalls/precompiles/weierstrass/double.rs vm/src/emulator/riscv/syscalls/syscall_context.rs vm/src/emulator/riscv/syscalls/unconstrained.rs vm/src/emulator/riscv/syscalls/verify.rs vm/src/emulator/riscv/syscalls/write.rs vm/src/emulator/stdin.rs vm/src/instances/mod.rs vm/src/instances/chiptype/mod.rs vm/src/instances/chiptype/chiptype_macros/mod.rs vm/src/instances/chiptype/chiptype_macros/define_chip_type.rs vm/src/instances/chiptype/chiptype_macros/enum_chip_type.rs vm/src/instances/chiptype/chiptype_macros/impl_air.rs vm/src/instances/chiptype/chiptype_macros/impl_base_air.rs vm/src/instances/chiptype/chiptype_macros/impl_chip_behavior.rs vm/src/instances/chiptype/recursion_chiptype.rs vm/src/instances/chiptype/riscv_chiptype.rs vm/src/instances/compiler/mod.rs vm/src/instances/compiler/onchain_circuit/mod.rs vm/src/instances/compiler/onchain_circuit/gnark/mod.rs vm/src/instances/compiler/onchain_circuit/gnark/builder.rs vm/src/instances/compiler/onchain_circuit/gnark/witness.rs vm/src/instances/compiler/onchain_circuit/stdin.rs vm/src/instances/compiler/onchain_circuit/utils.rs vm/src/instances/compiler/recursion_circuit/mod.rs vm/src/instances/compiler/recursion_circuit/combine/mod.rs vm/src/instances/compiler/recursion_circuit/combine/builder.rs vm/src/instances/compiler/recursion_circuit/compress/mod.rs vm/src/instances/compiler/recursion_circuit/compress/builder.rs vm/src/instances/compiler/recursion_circuit/embed/mod.rs vm/src/instances/compiler/recursion_circuit/embed/builder.rs vm/src/instances/compiler/recursion_circuit/stdin.rs vm/src/instances/compiler/riscv_circuit/mod.rs vm/src/instances/compiler/riscv_circuit/convert/mod.rs vm/src/instances/compiler/riscv_circuit/convert/builder.rs vm/src/instances/compiler/riscv_circuit/deferred/mod.rs vm/src/instances/compiler/riscv_circuit/deferred/builder.rs vm/src/instances/compiler/riscv_circuit/stdin.rs vm/src/instances/compiler/shapes/mod.rs vm/src/instances/compiler/shapes/recursion_shape.rs vm/src/instances/compiler/shapes/riscv_shape.rs vm/src/instances/compiler/simple_circuit/mod.rs vm/src/instances/compiler/simple_circuit/builder.rs vm/src/instances/compiler/simple_circuit/stdin.rs vm/src/instances/compiler/vk_merkle/mod.rs vm/src/instances/compiler/vk_merkle/builder.rs vm/src/instances/compiler/vk_merkle/stdin.rs vm/src/instances/compiler/witness.rs vm/src/instances/configs/mod.rs vm/src/instances/configs/embed_bb_bn254_poseidon2.rs vm/src/instances/configs/embed_kb_bn254_poseidon2.rs vm/src/instances/configs/recur_bb_poseidon2.rs vm/src/instances/configs/recur_kb_poseidon2.rs vm/src/instances/configs/riscv_bb_poseidon2.rs vm/src/instances/configs/riscv_kb_poseidon2.rs vm/src/instances/configs/riscv_m31_poseidon2.rs vm/src/instances/machine/mod.rs vm/src/instances/machine/combine.rs vm/src/instances/machine/compress.rs vm/src/instances/machine/convert.rs vm/src/instances/machine/deferred.rs vm/src/instances/machine/embed.rs vm/src/instances/machine/riscv.rs vm/src/instances/machine/simple.rs vm/src/iter/mod.rs vm/src/iter/rayon/mod.rs vm/src/iter/rayon/impls.rs vm/src/machine/mod.rs vm/src/machine/builder/mod.rs vm/src/machine/builder/base.rs vm/src/machine/builder/extension.rs vm/src/machine/builder/lookup.rs vm/src/machine/builder/permutation.rs vm/src/machine/builder/public_values.rs vm/src/machine/builder/range_check.rs vm/src/machine/builder/recursion.rs vm/src/machine/builder/riscv_memory.rs vm/src/machine/builder/scoped.rs vm/src/machine/builder/septic.rs vm/src/machine/builder/sub_builder.rs vm/src/machine/builder/word.rs vm/src/machine/chip.rs vm/src/machine/debug/mod.rs vm/src/machine/debug/constraints.rs vm/src/machine/debug/lookups.rs vm/src/machine/extension.rs vm/src/machine/field.rs vm/src/machine/folder.rs vm/src/machine/keys.rs vm/src/machine/logger.rs vm/src/machine/lookup.rs vm/src/machine/machine.rs vm/src/machine/permutation.rs vm/src/machine/proof.rs vm/src/machine/prover.rs vm/src/machine/septic/mod.rs vm/src/machine/septic/curve.rs vm/src/machine/septic/digest.rs vm/src/machine/septic/extension.rs vm/src/machine/septic/fields/mod.rs vm/src/machine/septic/fields/babybear.rs vm/src/machine/septic/fields/dummy.rs vm/src/machine/septic/fields/koalabear.rs vm/src/machine/septic/fields/mersenne31.rs vm/src/machine/utils.rs vm/src/machine/verifier.rs vm/src/machine/witness.rs vm/src/primitives/mod.rs vm/src/primitives/consts.rs vm/src/primitives/poseidon2/mod.rs vm/src/primitives/poseidon2/babybear.rs vm/src/primitives/poseidon2/koalabear.rs vm/src/primitives/poseidon2/mersenne31.rs vm/src/proverchain/mod.rs vm/src/proverchain/combine.rs vm/src/proverchain/compress.rs vm/src/proverchain/convert.rs vm/src/proverchain/embed.rs vm/src/proverchain/riscv.rs vm/src/proverchain/deferred.rs vm/src/thread/mod.rs vm/src/thread/channel.rs vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_bb.bin vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_kb.bin

vm/src/lib.rs:
vm/src/chips/mod.rs:
vm/src/chips/chips/mod.rs:
vm/src/chips/chips/alu/mod.rs:
vm/src/chips/chips/alu/add_sub/mod.rs:
vm/src/chips/chips/alu/add_sub/columns.rs:
vm/src/chips/chips/alu/add_sub/constraints.rs:
vm/src/chips/chips/alu/add_sub/traces.rs:
vm/src/chips/chips/alu/bitwise/mod.rs:
vm/src/chips/chips/alu/bitwise/columns.rs:
vm/src/chips/chips/alu/bitwise/constraints.rs:
vm/src/chips/chips/alu/bitwise/traces.rs:
vm/src/chips/chips/alu/divrem/mod.rs:
vm/src/chips/chips/alu/divrem/columns.rs:
vm/src/chips/chips/alu/divrem/constraints.rs:
vm/src/chips/chips/alu/divrem/traces.rs:
vm/src/chips/chips/alu/divrem/utils.rs:
vm/src/chips/chips/alu/event.rs:
vm/src/chips/chips/alu/lt/mod.rs:
vm/src/chips/chips/alu/lt/columns.rs:
vm/src/chips/chips/alu/lt/constraints.rs:
vm/src/chips/chips/alu/lt/traces.rs:
vm/src/chips/chips/alu/mul/mod.rs:
vm/src/chips/chips/alu/mul/columns.rs:
vm/src/chips/chips/alu/mul/constraints.rs:
vm/src/chips/chips/alu/mul/traces.rs:
vm/src/chips/chips/alu/sll/mod.rs:
vm/src/chips/chips/alu/sll/columns.rs:
vm/src/chips/chips/alu/sll/constraints.rs:
vm/src/chips/chips/alu/sll/traces.rs:
vm/src/chips/chips/alu/sr/mod.rs:
vm/src/chips/chips/alu/sr/columns.rs:
vm/src/chips/chips/alu/sr/constraints.rs:
vm/src/chips/chips/alu/sr/traces.rs:
vm/src/chips/chips/byte/mod.rs:
vm/src/chips/chips/byte/columns.rs:
vm/src/chips/chips/byte/constraints.rs:
vm/src/chips/chips/byte/event.rs:
vm/src/chips/chips/byte/traces.rs:
vm/src/chips/chips/byte/utils.rs:
vm/src/chips/chips/riscv_cpu/mod.rs:
vm/src/chips/chips/riscv_cpu/auipc/mod.rs:
vm/src/chips/chips/riscv_cpu/auipc/columns.rs:
vm/src/chips/chips/riscv_cpu/auipc/constraints.rs:
vm/src/chips/chips/riscv_cpu/auipc/traces.rs:
vm/src/chips/chips/riscv_cpu/branch/mod.rs:
vm/src/chips/chips/riscv_cpu/branch/columns.rs:
vm/src/chips/chips/riscv_cpu/branch/constraints.rs:
vm/src/chips/chips/riscv_cpu/branch/traces.rs:
vm/src/chips/chips/riscv_cpu/chunk_clk/mod.rs:
vm/src/chips/chips/riscv_cpu/chunk_clk/constraints.rs:
vm/src/chips/chips/riscv_cpu/chunk_clk/traces.rs:
vm/src/chips/chips/riscv_cpu/columns.rs:
vm/src/chips/chips/riscv_cpu/constraints.rs:
vm/src/chips/chips/riscv_cpu/ecall/mod.rs:
vm/src/chips/chips/riscv_cpu/ecall/columns.rs:
vm/src/chips/chips/riscv_cpu/ecall/constraints.rs:
vm/src/chips/chips/riscv_cpu/ecall/traces.rs:
vm/src/chips/chips/riscv_cpu/event.rs:
vm/src/chips/chips/riscv_cpu/instruction/mod.rs:
vm/src/chips/chips/riscv_cpu/instruction/columns.rs:
vm/src/chips/chips/riscv_cpu/jump/mod.rs:
vm/src/chips/chips/riscv_cpu/jump/columns.rs:
vm/src/chips/chips/riscv_cpu/jump/constraints.rs:
vm/src/chips/chips/riscv_cpu/jump/traces.rs:
vm/src/chips/chips/riscv_cpu/opcode_selector/mod.rs:
vm/src/chips/chips/riscv_cpu/opcode_selector/columns.rs:
vm/src/chips/chips/riscv_cpu/opcode_specific/mod.rs:
vm/src/chips/chips/riscv_cpu/opcode_specific/columns.rs:
vm/src/chips/chips/riscv_cpu/public_values/mod.rs:
vm/src/chips/chips/riscv_cpu/public_values/constraints.rs:
vm/src/chips/chips/riscv_cpu/register/mod.rs:
vm/src/chips/chips/riscv_cpu/register/constraints.rs:
vm/src/chips/chips/riscv_cpu/traces.rs:
vm/src/chips/chips/riscv_cpu/utils.rs:
vm/src/chips/chips/riscv_global/mod.rs:
vm/src/chips/chips/riscv_global/columns.rs:
vm/src/chips/chips/riscv_global/constraints.rs:
vm/src/chips/chips/riscv_global/event.rs:
vm/src/chips/chips/riscv_global/traces.rs:
vm/src/chips/chips/riscv_memory/mod.rs:
vm/src/chips/chips/riscv_memory/event.rs:
vm/src/chips/chips/riscv_memory/initialize_finalize/mod.rs:
vm/src/chips/chips/riscv_memory/initialize_finalize/columns.rs:
vm/src/chips/chips/riscv_memory/initialize_finalize/constraints.rs:
vm/src/chips/chips/riscv_memory/initialize_finalize/traces.rs:
vm/src/chips/chips/riscv_memory/local/mod.rs:
vm/src/chips/chips/riscv_memory/local/columns.rs:
vm/src/chips/chips/riscv_memory/local/constraints.rs:
vm/src/chips/chips/riscv_memory/local/traces.rs:
vm/src/chips/chips/riscv_memory/read_write/mod.rs:
vm/src/chips/chips/riscv_memory/read_write/columns.rs:
vm/src/chips/chips/riscv_memory/read_write/constraints.rs:
vm/src/chips/chips/riscv_memory/read_write/traces.rs:
vm/src/chips/chips/riscv_poseidon2/mod.rs:
vm/src/chips/chips/riscv_poseidon2/constraints.rs:
vm/src/chips/chips/riscv_poseidon2/event.rs:
vm/src/chips/chips/riscv_poseidon2/traces.rs:
vm/src/chips/chips/riscv_program/mod.rs:
vm/src/chips/chips/riscv_program/columns.rs:
vm/src/chips/chips/riscv_program/constraints.rs:
vm/src/chips/chips/riscv_program/traces.rs:
vm/src/chips/chips/toys/mod.rs:
vm/src/chips/chips/toys/lookup_toy.rs:
vm/src/chips/chips/toys/toy.rs:
vm/src/chips/chips/events.rs:
vm/src/chips/chips/alu_base/mod.rs:
vm/src/chips/chips/alu_base/columns.rs:
vm/src/chips/chips/alu_base/constraints.rs:
vm/src/chips/chips/alu_base/traces.rs:
vm/src/chips/chips/alu_ext/mod.rs:
vm/src/chips/chips/alu_ext/columns.rs:
vm/src/chips/chips/alu_ext/constraints.rs:
vm/src/chips/chips/alu_ext/traces.rs:
vm/src/chips/chips/batch_fri/mod.rs:
vm/src/chips/chips/batch_fri/columns.rs:
vm/src/chips/chips/batch_fri/constraints.rs:
vm/src/chips/chips/batch_fri/traces.rs:
vm/src/chips/chips/exp_reverse_bits/mod.rs:
vm/src/chips/chips/exp_reverse_bits/columns.rs:
vm/src/chips/chips/exp_reverse_bits/constraints.rs:
vm/src/chips/chips/exp_reverse_bits/traces.rs:
vm/src/chips/chips/poseidon2/mod.rs:
vm/src/chips/chips/poseidon2/constraints.rs:
vm/src/chips/chips/poseidon2/traces.rs:
vm/src/chips/chips/public_values/mod.rs:
vm/src/chips/chips/public_values/columns.rs:
vm/src/chips/chips/public_values/constraints.rs:
vm/src/chips/chips/public_values/traces.rs:
vm/src/chips/chips/recursion_memory/mod.rs:
vm/src/chips/chips/recursion_memory/constant/mod.rs:
vm/src/chips/chips/recursion_memory/constant/columns.rs:
vm/src/chips/chips/recursion_memory/constant/constraints.rs:
vm/src/chips/chips/recursion_memory/constant/traces.rs:
vm/src/chips/chips/recursion_memory/variable/mod.rs:
vm/src/chips/chips/recursion_memory/variable/columns.rs:
vm/src/chips/chips/recursion_memory/variable/constraints.rs:
vm/src/chips/chips/recursion_memory/variable/traces.rs:
vm/src/chips/chips/select/mod.rs:
vm/src/chips/chips/select/columns.rs:
vm/src/chips/chips/select/constraints.rs:
vm/src/chips/chips/select/trace.rs:
vm/src/chips/chips/syscall/mod.rs:
vm/src/chips/chips/syscall/columns.rs:
vm/src/chips/chips/syscall/constraints.rs:
vm/src/chips/chips/syscall/traces.rs:
vm/src/chips/gadgets/mod.rs:
vm/src/chips/gadgets/add.rs:
vm/src/chips/gadgets/add4.rs:
vm/src/chips/gadgets/add5.rs:
vm/src/chips/gadgets/and.rs:
vm/src/chips/gadgets/curves/mod.rs:
vm/src/chips/gadgets/curves/edwards/mod.rs:
vm/src/chips/gadgets/curves/edwards/ed25519.rs:
vm/src/chips/gadgets/curves/scalar_mul.rs:
vm/src/chips/gadgets/curves/weierstrass/mod.rs:
vm/src/chips/gadgets/curves/weierstrass/bls381.rs:
vm/src/chips/gadgets/curves/weierstrass/bn254.rs:
vm/src/chips/gadgets/curves/weierstrass/secp256k1.rs:
vm/src/chips/gadgets/field/mod.rs:
vm/src/chips/gadgets/field/bls381.rs:
vm/src/chips/gadgets/field/bn254.rs:
vm/src/chips/gadgets/field/field_den.rs:
vm/src/chips/gadgets/field/field_inner_product.rs:
vm/src/chips/gadgets/field/field_lt.rs:
vm/src/chips/gadgets/field/field_op.rs:
vm/src/chips/gadgets/field/field_sqrt.rs:
vm/src/chips/gadgets/field/secp256k1.rs:
vm/src/chips/gadgets/field/utils.rs:
vm/src/chips/gadgets/field_range_check/mod.rs:
vm/src/chips/gadgets/field_range_check/bit_decomposition.rs:
vm/src/chips/gadgets/field_range_check/word_range.rs:
vm/src/chips/gadgets/fixed_rotate_right.rs:
vm/src/chips/gadgets/fixed_shift_right.rs:
vm/src/chips/gadgets/global_accumulation.rs:
vm/src/chips/gadgets/global_interaction.rs:
vm/src/chips/gadgets/is_equal_word.rs:
vm/src/chips/gadgets/is_zero.rs:
vm/src/chips/gadgets/is_zero_word.rs:
vm/src/chips/gadgets/lt.rs:
vm/src/chips/gadgets/not.rs:
vm/src/chips/gadgets/poseidon2/mod.rs:
vm/src/chips/gadgets/poseidon2/columns.rs:
vm/src/chips/gadgets/poseidon2/constants.rs:
vm/src/chips/gadgets/poseidon2/constraints.rs:
vm/src/chips/gadgets/poseidon2/traces.rs:
vm/src/chips/gadgets/poseidon2/utils.rs:
vm/src/chips/gadgets/uint256/mod.rs:
vm/src/chips/gadgets/utils/mod.rs:
vm/src/chips/gadgets/utils/conversions.rs:
vm/src/chips/gadgets/utils/field_params.rs:
vm/src/chips/gadgets/utils/limbs.rs:
vm/src/chips/gadgets/utils/polynomial.rs:
vm/src/chips/gadgets/xor.rs:
vm/src/chips/precompiles/mod.rs:
vm/src/chips/precompiles/edwards/mod.rs:
vm/src/chips/precompiles/edwards/ed_add.rs:
vm/src/chips/precompiles/edwards/ed_decompress.rs:
vm/src/chips/precompiles/fptower/mod.rs:
vm/src/chips/precompiles/fptower/fp.rs:
vm/src/chips/precompiles/fptower/fp2_addsub.rs:
vm/src/chips/precompiles/fptower/fp2_mul.rs:
vm/src/chips/precompiles/keccak256/mod.rs:
vm/src/chips/precompiles/keccak256/columns.rs:
vm/src/chips/precompiles/keccak256/constraint.rs:
vm/src/chips/precompiles/keccak256/traces.rs:
vm/src/chips/precompiles/poseidon2/mod.rs:
vm/src/chips/precompiles/poseidon2/columns.rs:
vm/src/chips/precompiles/poseidon2/constraints.rs:
vm/src/chips/precompiles/poseidon2/traces.rs:
vm/src/chips/precompiles/sha256/mod.rs:
vm/src/chips/precompiles/sha256/compress/mod.rs:
vm/src/chips/precompiles/sha256/compress/columns.rs:
vm/src/chips/precompiles/sha256/compress/constraints.rs:
vm/src/chips/precompiles/sha256/compress/trace.rs:
vm/src/chips/precompiles/sha256/extend/mod.rs:
vm/src/chips/precompiles/sha256/extend/columns.rs:
vm/src/chips/precompiles/sha256/extend/constraints.rs:
vm/src/chips/precompiles/sha256/extend/flags.rs:
vm/src/chips/precompiles/sha256/extend/trace.rs:
vm/src/chips/precompiles/uint256/mod.rs:
vm/src/chips/precompiles/uint256/columns.rs:
vm/src/chips/precompiles/uint256/constraints.rs:
vm/src/chips/precompiles/uint256/traces.rs:
vm/src/chips/precompiles/weierstrass/mod.rs:
vm/src/chips/precompiles/weierstrass/weierstrass_add.rs:
vm/src/chips/precompiles/weierstrass/weierstrass_decompress.rs:
vm/src/chips/precompiles/weierstrass/weierstrass_double.rs:
vm/src/chips/trace.rs:
vm/src/chips/utils.rs:
vm/src/compiler/mod.rs:
vm/src/compiler/program.rs:
vm/src/compiler/recursion/mod.rs:
vm/src/compiler/recursion/circuit/mod.rs:
vm/src/compiler/recursion/circuit/builder.rs:
vm/src/compiler/recursion/circuit/challenger.rs:
vm/src/compiler/recursion/circuit/config.rs:
vm/src/compiler/recursion/circuit/constraints.rs:
vm/src/compiler/recursion/circuit/domain.rs:
vm/src/compiler/recursion/circuit/fri.rs:
vm/src/compiler/recursion/circuit/hash.rs:
vm/src/compiler/recursion/circuit/merkle_tree.rs:
vm/src/compiler/recursion/circuit/stark.rs:
vm/src/compiler/recursion/circuit/types.rs:
vm/src/compiler/recursion/circuit/utils.rs:
vm/src/compiler/recursion/circuit/witness/mod.rs:
vm/src/compiler/recursion/circuit/witness/embed.rs:
vm/src/compiler/recursion/circuit/witness/stark.rs:
vm/src/compiler/recursion/circuit/witness/witnessable.rs:
vm/src/compiler/recursion/constraints/mod.rs:
vm/src/compiler/recursion/constraints/opcodes.rs:
vm/src/compiler/recursion/instruction.rs:
vm/src/compiler/recursion/ir/mod.rs:
vm/src/compiler/recursion/ir/arithmetic.rs:
vm/src/compiler/recursion/ir/bits.rs:
vm/src/compiler/recursion/ir/block.rs:
vm/src/compiler/recursion/ir/builder.rs:
vm/src/compiler/recursion/ir/collections.rs:
vm/src/compiler/recursion/ir/compiler.rs:
vm/src/compiler/recursion/ir/instructions.rs:
vm/src/compiler/recursion/ir/ptr.rs:
vm/src/compiler/recursion/ir/symbolic.rs:
vm/src/compiler/recursion/ir/types.rs:
vm/src/compiler/recursion/ir/utils.rs:
vm/src/compiler/recursion/ir/var.rs:
vm/src/compiler/recursion/program.rs:
vm/src/compiler/recursion/types.rs:
vm/src/compiler/riscv/mod.rs:
vm/src/compiler/riscv/compiler.rs:
vm/src/compiler/riscv/disassembler/mod.rs:
vm/src/compiler/riscv/disassembler/elf.rs:
vm/src/compiler/riscv/disassembler/rrs.rs:
vm/src/compiler/riscv/instruction.rs:
vm/src/compiler/riscv/opcode.rs:
vm/src/compiler/riscv/program.rs:
vm/src/compiler/riscv/register.rs:
vm/src/compiler/word.rs:
vm/src/configs/mod.rs:
vm/src/configs/config.rs:
vm/src/configs/field_config/mod.rs:
vm/src/configs/field_config/bb_bn254.rs:
vm/src/configs/field_config/bb_simple.rs:
vm/src/configs/field_config/kb_bn254.rs:
vm/src/configs/field_config/kb_simple.rs:
vm/src/configs/stark_config/mod.rs:
vm/src/configs/stark_config/bb_bn254_poseidon2.rs:
vm/src/configs/stark_config/bb_poseidon2.rs:
vm/src/configs/stark_config/kb_bn254_poseidon2.rs:
vm/src/configs/stark_config/kb_poseidon2.rs:
vm/src/configs/stark_config/m31_poseidon2.rs:
vm/src/emulator/mod.rs:
vm/src/emulator/emulator.rs:
vm/src/emulator/opts.rs:
vm/src/emulator/record.rs:
vm/src/emulator/recursion/mod.rs:
vm/src/emulator/recursion/emulator/mod.rs:
vm/src/emulator/recursion/emulator/memory.rs:
vm/src/emulator/recursion/emulator/opcode.rs:
vm/src/emulator/recursion/public_values.rs:
vm/src/emulator/recursion/record.rs:
vm/src/emulator/riscv/mod.rs:
vm/src/emulator/riscv/emulator/mod.rs:
vm/src/emulator/riscv/emulator/error.rs:
vm/src/emulator/riscv/emulator/instruction.rs:
vm/src/emulator/riscv/emulator/instruction_simple.rs:
vm/src/emulator/riscv/emulator/mode.rs:
vm/src/emulator/riscv/emulator/unconstrained.rs:
vm/src/emulator/riscv/emulator/util.rs:
vm/src/emulator/riscv/hook/mod.rs:
vm/src/emulator/riscv/hook/ecrecover.rs:
vm/src/emulator/riscv/hook/ed_decompress.rs:
vm/src/emulator/riscv/memory.rs:
vm/src/emulator/riscv/public_values.rs:
vm/src/emulator/riscv/record.rs:
vm/src/emulator/riscv/state.rs:
vm/src/emulator/riscv/syscalls/mod.rs:
vm/src/emulator/riscv/syscalls/code.rs:
vm/src/emulator/riscv/syscalls/commit.rs:
vm/src/emulator/riscv/syscalls/deferred.rs:
vm/src/emulator/riscv/syscalls/halt.rs:
vm/src/emulator/riscv/syscalls/hint.rs:
vm/src/emulator/riscv/syscalls/precompiles/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/ec/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/ec/event.rs:
vm/src/emulator/riscv/syscalls/precompiles/edwards/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/edwards/add.rs:
vm/src/emulator/riscv/syscalls/precompiles/edwards/decompress.rs:
vm/src/emulator/riscv/syscalls/precompiles/edwards/event.rs:
vm/src/emulator/riscv/syscalls/precompiles/fptower/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/fptower/event.rs:
vm/src/emulator/riscv/syscalls/precompiles/fptower/fp.rs:
vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_addsub.rs:
vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_mul.rs:
vm/src/emulator/riscv/syscalls/precompiles/keccak256/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/keccak256/event.rs:
vm/src/emulator/riscv/syscalls/precompiles/keccak256/permute.rs:
vm/src/emulator/riscv/syscalls/precompiles/poseidon2/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/poseidon2/event.rs:
vm/src/emulator/riscv/syscalls/precompiles/poseidon2/permute.rs:
vm/src/emulator/riscv/syscalls/precompiles/sha256/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/sha256/compress.rs:
vm/src/emulator/riscv/syscalls/precompiles/sha256/event.rs:
vm/src/emulator/riscv/syscalls/precompiles/sha256/extend.rs:
vm/src/emulator/riscv/syscalls/precompiles/uint256/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/uint256/event.rs:
vm/src/emulator/riscv/syscalls/precompiles/uint256/syscall.rs:
vm/src/emulator/riscv/syscalls/precompiles/weierstrass/mod.rs:
vm/src/emulator/riscv/syscalls/precompiles/weierstrass/add.rs:
vm/src/emulator/riscv/syscalls/precompiles/weierstrass/decompress.rs:
vm/src/emulator/riscv/syscalls/precompiles/weierstrass/double.rs:
vm/src/emulator/riscv/syscalls/syscall_context.rs:
vm/src/emulator/riscv/syscalls/unconstrained.rs:
vm/src/emulator/riscv/syscalls/verify.rs:
vm/src/emulator/riscv/syscalls/write.rs:
vm/src/emulator/stdin.rs:
vm/src/instances/mod.rs:
vm/src/instances/chiptype/mod.rs:
vm/src/instances/chiptype/chiptype_macros/mod.rs:
vm/src/instances/chiptype/chiptype_macros/define_chip_type.rs:
vm/src/instances/chiptype/chiptype_macros/enum_chip_type.rs:
vm/src/instances/chiptype/chiptype_macros/impl_air.rs:
vm/src/instances/chiptype/chiptype_macros/impl_base_air.rs:
vm/src/instances/chiptype/chiptype_macros/impl_chip_behavior.rs:
vm/src/instances/chiptype/recursion_chiptype.rs:
vm/src/instances/chiptype/riscv_chiptype.rs:
vm/src/instances/compiler/mod.rs:
vm/src/instances/compiler/onchain_circuit/mod.rs:
vm/src/instances/compiler/onchain_circuit/gnark/mod.rs:
vm/src/instances/compiler/onchain_circuit/gnark/builder.rs:
vm/src/instances/compiler/onchain_circuit/gnark/witness.rs:
vm/src/instances/compiler/onchain_circuit/stdin.rs:
vm/src/instances/compiler/onchain_circuit/utils.rs:
vm/src/instances/compiler/recursion_circuit/mod.rs:
vm/src/instances/compiler/recursion_circuit/combine/mod.rs:
vm/src/instances/compiler/recursion_circuit/combine/builder.rs:
vm/src/instances/compiler/recursion_circuit/compress/mod.rs:
vm/src/instances/compiler/recursion_circuit/compress/builder.rs:
vm/src/instances/compiler/recursion_circuit/embed/mod.rs:
vm/src/instances/compiler/recursion_circuit/embed/builder.rs:
vm/src/instances/compiler/recursion_circuit/stdin.rs:
vm/src/instances/compiler/riscv_circuit/mod.rs:
vm/src/instances/compiler/riscv_circuit/convert/mod.rs:
vm/src/instances/compiler/riscv_circuit/convert/builder.rs:
vm/src/instances/compiler/riscv_circuit/deferred/mod.rs:
vm/src/instances/compiler/riscv_circuit/deferred/builder.rs:
vm/src/instances/compiler/riscv_circuit/stdin.rs:
vm/src/instances/compiler/shapes/mod.rs:
vm/src/instances/compiler/shapes/recursion_shape.rs:
vm/src/instances/compiler/shapes/riscv_shape.rs:
vm/src/instances/compiler/simple_circuit/mod.rs:
vm/src/instances/compiler/simple_circuit/builder.rs:
vm/src/instances/compiler/simple_circuit/stdin.rs:
vm/src/instances/compiler/vk_merkle/mod.rs:
vm/src/instances/compiler/vk_merkle/builder.rs:
vm/src/instances/compiler/vk_merkle/stdin.rs:
vm/src/instances/compiler/witness.rs:
vm/src/instances/configs/mod.rs:
vm/src/instances/configs/embed_bb_bn254_poseidon2.rs:
vm/src/instances/configs/embed_kb_bn254_poseidon2.rs:
vm/src/instances/configs/recur_bb_poseidon2.rs:
vm/src/instances/configs/recur_kb_poseidon2.rs:
vm/src/instances/configs/riscv_bb_poseidon2.rs:
vm/src/instances/configs/riscv_kb_poseidon2.rs:
vm/src/instances/configs/riscv_m31_poseidon2.rs:
vm/src/instances/machine/mod.rs:
vm/src/instances/machine/combine.rs:
vm/src/instances/machine/compress.rs:
vm/src/instances/machine/convert.rs:
vm/src/instances/machine/deferred.rs:
vm/src/instances/machine/embed.rs:
vm/src/instances/machine/riscv.rs:
vm/src/instances/machine/simple.rs:
vm/src/iter/mod.rs:
vm/src/iter/rayon/mod.rs:
vm/src/iter/rayon/impls.rs:
vm/src/machine/mod.rs:
vm/src/machine/builder/mod.rs:
vm/src/machine/builder/base.rs:
vm/src/machine/builder/extension.rs:
vm/src/machine/builder/lookup.rs:
vm/src/machine/builder/permutation.rs:
vm/src/machine/builder/public_values.rs:
vm/src/machine/builder/range_check.rs:
vm/src/machine/builder/recursion.rs:
vm/src/machine/builder/riscv_memory.rs:
vm/src/machine/builder/scoped.rs:
vm/src/machine/builder/septic.rs:
vm/src/machine/builder/sub_builder.rs:
vm/src/machine/builder/word.rs:
vm/src/machine/chip.rs:
vm/src/machine/debug/mod.rs:
vm/src/machine/debug/constraints.rs:
vm/src/machine/debug/lookups.rs:
vm/src/machine/extension.rs:
vm/src/machine/field.rs:
vm/src/machine/folder.rs:
vm/src/machine/keys.rs:
vm/src/machine/logger.rs:
vm/src/machine/lookup.rs:
vm/src/machine/machine.rs:
vm/src/machine/permutation.rs:
vm/src/machine/proof.rs:
vm/src/machine/prover.rs:
vm/src/machine/septic/mod.rs:
vm/src/machine/septic/curve.rs:
vm/src/machine/septic/digest.rs:
vm/src/machine/septic/extension.rs:
vm/src/machine/septic/fields/mod.rs:
vm/src/machine/septic/fields/babybear.rs:
vm/src/machine/septic/fields/dummy.rs:
vm/src/machine/septic/fields/koalabear.rs:
vm/src/machine/septic/fields/mersenne31.rs:
vm/src/machine/utils.rs:
vm/src/machine/verifier.rs:
vm/src/machine/witness.rs:
vm/src/primitives/mod.rs:
vm/src/primitives/consts.rs:
vm/src/primitives/poseidon2/mod.rs:
vm/src/primitives/poseidon2/babybear.rs:
vm/src/primitives/poseidon2/koalabear.rs:
vm/src/primitives/poseidon2/mersenne31.rs:
vm/src/proverchain/mod.rs:
vm/src/proverchain/combine.rs:
vm/src/proverchain/compress.rs:
vm/src/proverchain/convert.rs:
vm/src/proverchain/embed.rs:
vm/src/proverchain/riscv.rs:
vm/src/proverchain/deferred.rs:
vm/src/thread/mod.rs:
vm/src/thread/channel.rs:
vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_bb.bin:
vm/src/instances/compiler/vk_merkle/../shape_vk_bins/vk_map_kb.bin:
