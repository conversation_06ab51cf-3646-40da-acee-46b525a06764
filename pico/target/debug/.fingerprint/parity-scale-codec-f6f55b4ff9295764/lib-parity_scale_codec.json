{"rustc": 9359135195555611964, "features": "[\"derive\", \"max-encoded-len\", \"parity-scale-codec-derive\"]", "declared_features": "[\"arbitrary\", \"bit-vec\", \"bitvec\", \"bytes\", \"chain-error\", \"default\", \"derive\", \"full\", \"fuzz\", \"generic-array\", \"max-encoded-len\", \"parity-scale-codec-derive\", \"serde\", \"std\"]", "target": 9871095153422177575, "profile": 15657897354478470176, "path": 16962795301193558123, "deps": [[227352626082989353, "impl_trait_for_tuples", false, 18200907224547166614], [2161086476230812269, "build_script_build", false, 4820064518720720792], [3646984927696731388, "byte_slice_cast", false, 1962980269147155956], [12049092979829613636, "parity_scale_codec_derive", false, 10397625771714608950], [13733475071365806210, "const_format", false, 12897037056602229118], [13847662864258534762, "arrayvec", false, 6118413465140507884]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/parity-scale-codec-f6f55b4ff9295764/dep-lib-parity_scale_codec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}