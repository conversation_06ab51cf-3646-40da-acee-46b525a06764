{"rustc": 9359135195555611964, "features": "[\"derive\", \"scale-info-derive\"]", "declared_features": "[\"bit-vec\", \"bitvec\", \"decode\", \"default\", \"derive\", \"docs\", \"scale-info-derive\", \"schema\", \"schemars\", \"serde\", \"std\"]", "target": 5176310362719024951, "profile": 15657897354478470176, "path": 4810017886893231890, "deps": [[2161086476230812269, "scale", false, 10644400639979133649], [2828590642173593838, "cfg_if", false, 10891327968397332491], [11137972157736446730, "scale_info_derive", false, 6929922474965201849], [13487854193495724092, "derive_more", false, 15230923415243839671]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/scale-info-4641306a2d2b9baf/dep-lib-scale_info", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}