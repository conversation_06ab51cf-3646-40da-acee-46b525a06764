{"rustc": 9359135195555611964, "features": "[\"bls381\"]", "declared_features": "[\"all\", \"anssi\", \"bench\", \"bls24\", \"bls381\", \"bls383\", \"bls461\", \"bls48\", \"bn254\", \"bn254cx\", \"brainpool\", \"c25519\", \"c41417\", \"default\", \"ed25519\", \"fp256bn\", \"fp512bn\", \"goldilocks\", \"hifive\", \"nist256\", \"nist384\", \"nist521\", \"nums256e\", \"nums256w\", \"nums384e\", \"nums384w\", \"nums512e\", \"nums512w\", \"rsa2048\", \"rsa3072\", \"rsa4096\", \"secp256k1\", \"std\"]", "target": 15029764323986202181, "profile": 15657897354478470176, "path": 16456481483125237884, "deps": [[2161086476230812269, "codec", false, 10644400639979133649], [14281265408697611084, "scale_info", false, 6721113100256125396]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/snowbridge-amcl-7641db562e727a70/dep-lib-snowbridge_amcl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}