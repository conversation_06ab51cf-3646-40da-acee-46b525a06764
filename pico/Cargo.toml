[workspace]
members = ["derive", "vm", "sdk/*", "scripts", "gnark/field-ffi", "perf"]
resolver = "2"

[workspace.package]
version = "1.1.6"
edition = "2021"
license = "MIT OR Apache-2.0"
keywords = ["zkvm", "plonky3", "stark", "FRI"]
categories = ["cryptography"]
repository = "https://github.com/brevis-network/pico"

# Run the benchmarks in perf folder.
[profile.perf]
inherits = "release"
opt-level = 3
lto = true
codegen-units = 1

[profile.profiling]
inherits = "release"
#debug = "limited"
strip = "none"

[profile.fast]
inherits = "release"
debug = true
debug-assertions = true

[workspace.dependencies]
pico-derive = { path = "derive" }
pico-vm = { path = "vm" }
pico-sdk = { path = "sdk/sdk" }
pico-cli = { path = "sdk/cli" }
pico-patch-libs = { path = "sdk/patch-libs" }
pico-perf = { path = "perf" }

# p3

# NOTE: Pin to known a "good" commit without recent API changes
p3-air = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-field = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-commit = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-matrix = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-baby-bear = { git = "https://github.com/brevis-network/Plonky3.git", features = [
  "nightly-features",
], rev = "a4d376b" }
p3-koala-bear = { git = "https://github.com/brevis-network/Plonky3.git", features = [
  "nightly-features",
], rev = "a4d376b" }
p3-util = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-challenger = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-dft = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-fri = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-goldilocks = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-keccak = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-keccak-air = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-blake3 = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-mds = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-merkle-tree = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-poseidon2 = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-symmetric = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-uni-stark = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-maybe-rayon = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-bn254-fr = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-mersenne-31 = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }
p3-circle = { git = "https://github.com/brevis-network/Plonky3.git", rev = "a4d376b" }

# For local debugging
# p3-air = { path = "../p3/air" }
# p3-field = { path = "../p3/field" }
# p3-commit = { path = "../p3/commit" }
# p3-matrix = { path = "../p3/matrix" }
# p3-baby-bear = { path = "../p3/baby-bear", features = ["nightly-features"] }
# p3-koala-bear = { path = "../p3/koala-bear", features = ["nightly-features"] }
# p3-util = { path = "../p3/util" }
# p3-challenger = { path = "../p3/challenger" }
# p3-dft = { path = "../p3/dft" }
# p3-fri = { path = "../p3/fri" }
# p3-goldilocks = { path = "../p3/goldilocks" }
# p3-keccak = { path = "../p3/keccak" }
# p3-keccak-air = { path = "../p3/keccak-air" }
# p3-blake3 = { path = "../p3/blake3" }
# p3-mds = { path = "../p3/mds" }
# p3-merkle-tree = { path = "../p3/merkle-tree" }
# p3-poseidon2 = { path = "../p3/poseidon2" }
# p3-symmetric = { path = "../p3/symmetric" }
# p3-uni-stark = { path = "../p3/uni-stark" }
# p3-maybe-rayon = { path = "../p3/maybe-rayon" }
# p3-bn254-fr = { path = "../p3/bn254-fr" }
# p3-mersenne-31 = { path = "../p3/mersenne-31" }
# p3-circle = { path = "../p3/circle" }

# misc
amcl = { package = "snowbridge-amcl", version = "1.0.2", default-features = false, features = ["bls381"] }
anyhow = { version = "1.0.40", default-features = false }
arrayref = "0.3.8"
backtrace = "0.3.71"
bincode = "1.3.3"
bytemuck = "1.16.3"
cfg-if = "1.0.0"
clap = { version = "4.5.13", features = ["derive", "env"] }
cpu-time = "1.0.0"
crossbeam = "0.8.4"
core_affinity = "0.8"
csv = "1.3"
curve25519-dalek = { version = "4.1.2" }
dashmap = "6.1.0"
dashu = "0.4.2"
derive_more = { version = "2.0", features = ["constructor"] }
elf = "0.7.4"
elliptic-curve = "0.13.8"
env_logger = "0.11.6"
eyre = "0.6.12"
ff = { version = "0.13", features = ["derive", "derive_bits"] }
halo2curves = "0.7.0"
hashbrown = { version = "0.14.5", features = ["serde", "inline-more"] }
hex = "0.4.3"
hybrid-array = "0.2.1"
itertools = "0.13"
k256 = { version = "0.13.3", features = ["expose-field"] }
lazy_static = "1.5.0"
log = "0.4.21"
num = { version = "0.4.3" }
num-bigint = "0.4.6"
num-traits = "0.2"
num_cpus = "1.16"
once_cell = "1.20"
p256 = { version = "0.13.2", features = ["expose-field"] }
paste = "1.0.15"
proc-macro2 = "1.0"
quote = "1.0"
rand = "0.8.5"
rayon = "1.10"
rayon-scan = "0.1.1"
reqwest = { version = "0.12.9", features = ["blocking"] }
rrs_lib = { package = "rrs-succinct", version = "0.1.0" }
rug = "1.26.1"
serde = { version = "1.0.205", features = ["derive", "rc"] }
serde_json = "1.0.121"
serde_with = "3.9.0"
static_assertions = "1.1"
strum = { version = "0.26.3", features = ["derive"] }
strum_macros = "0.26.4"
syn = { version = "1.0", features = ["full"] }
sysinfo = "0.30.13"
thiserror = "1.0.63"
tikv-jemallocator = "0.6"
tiny-keccak = { version = "2.0.2", features = ["keccak"] }
tracing = "0.1.40"
tracing-forest = { version = "0.1.6", features = ["ansi", "smallvec"] }
tracing-subscriber = { version = "0.3.18", features = ["std", "env-filter"] }
typenum = "1.17.0"
vec_map = { version = "0.8.2", features = ["serde"] }
zkhash = "0.2.0"
