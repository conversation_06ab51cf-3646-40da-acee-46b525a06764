# Brevis Pico ZKVM contest details

- Join [<PERSON> Disco<PERSON>](https://discord.gg/MABEWyASkp)
- Submit findings using the **Issues** page in your private contest repo (label issues as **Medium** or **High**)
- [Read for more details](https://docs.sherlock.xyz/audits/watsons)

# Q&A

### Q: On what chains are the smart contracts going to be deployed?
N/A
___

### Q: If you are integrating tokens, are you allowing only whitelisted tokens to work with the codebase or any complying with the standard? Are they assumed to have certain properties, e.g. be non-reentrant? Are there any types of [weird tokens](https://github.com/d-xo/weird-erc20) you want to integrate?
N/A
___

### Q: Are there any limitations on values set by admins (or other roles) in the codebase, including restrictions on array lengths?
N/A
___

### Q: Are there any limitations on values set by admins (or other roles) in protocols you integrate with, including restrictions on array lengths?
N/A
___

### Q: Is the codebase expected to comply with any specific EIPs?
No.
___

### Q: Are there any off-chain mechanisms involved in the protocol (e.g., keeper bots, arbitrage bots, etc.)? We assume these mechanisms will not misbehave, delay, or go offline unless otherwise specified.
No.
___

### Q: What properties/invariants do you want to hold even if breaking them has a low/unknown impact?
No.
___

### Q: Please discuss any design choices you made.
N/A
___

### Q: Please provide links to previous audits (if any) and all the known issues or acceptable risks.
N/A
___

### Q: Please list any relevant protocol resources.
Pico docs: https://docs.brevis.network/
___

### Q: Additional audit information.
N/A


# Audit scope

[pico @ 3046e89b1c7bf065b7932d859a06a24920d57531](https://github.com/brevis-network/pico/tree/3046e89b1c7bf065b7932d859a06a24920d57531)
- [pico/derive/Cargo.toml](pico/derive/Cargo.toml)
- [pico/derive/src/lib.rs](pico/derive/src/lib.rs)
- [pico/gnark/babybear/babybear.go](pico/gnark/babybear/babybear.go)
- [pico/gnark/babybear_verifier/verifier.go](pico/gnark/babybear_verifier/verifier.go)
- [pico/gnark/field-ffi/src/babybear.rs](pico/gnark/field-ffi/src/babybear.rs)
- [pico/gnark/field-ffi/src/koalabear.rs](pico/gnark/field-ffi/src/koalabear.rs)
- [pico/gnark/field-ffi/src/lib.rs](pico/gnark/field-ffi/src/lib.rs)
- [pico/gnark/go.mod](pico/gnark/go.mod)
- [pico/gnark/go.sum](pico/gnark/go.sum)
- [pico/gnark/koalabear/koalabear.go](pico/gnark/koalabear/koalabear.go)
- [pico/gnark/koalabear_verifier/verifier.go](pico/gnark/koalabear_verifier/verifier.go)
- [pico/gnark/poseidon2/constants.go](pico/gnark/poseidon2/constants.go)
- [pico/gnark/poseidon2/poseidon2_babybear.go](pico/gnark/poseidon2/poseidon2_babybear.go)
- [pico/gnark/poseidon2/poseidon2.go](pico/gnark/poseidon2/poseidon2.go)
- [pico/gnark/poseidon2/poseidon2_koalabear.go](pico/gnark/poseidon2/poseidon2_koalabear.go)
- [pico/gnark/poseidon2/utils.go](pico/gnark/poseidon2/utils.go)
- [pico/gnark/README.md](pico/gnark/README.md)
- [pico/gnark/sdk/babybear_cmd.go](pico/gnark/sdk/babybear_cmd.go)
- [pico/gnark/sdk/common.go](pico/gnark/sdk/common.go)
- [pico/gnark/sdk/koalabear_cmd.go](pico/gnark/sdk/koalabear_cmd.go)
- [pico/gnark/sdk/main/main.go](pico/gnark/sdk/main/main.go)
- [pico/gnark/server/main/main.go](pico/gnark/server/main/main.go)
- [pico/gnark/utils/constraints.go](pico/gnark/utils/constraints.go)
- [pico/scripts/src/bin/build_vk_map.rs](pico/scripts/src/bin/build_vk_map.rs)
- [pico/sdk/cli/src/bin/cargo-pico.rs](pico/sdk/cli/src/bin/cargo-pico.rs)
- [pico/sdk/cli/src/build/build.rs](pico/sdk/cli/src/build/build.rs)
- [pico/sdk/cli/src/build/client.rs](pico/sdk/cli/src/build/client.rs)
- [pico/sdk/cli/src/build/mod.rs](pico/sdk/cli/src/build/mod.rs)
- [pico/sdk/cli/src/lib.rs](pico/sdk/cli/src/lib.rs)
- [pico/sdk/cli/src/subcommand/build.rs](pico/sdk/cli/src/subcommand/build.rs)
- [pico/sdk/cli/src/subcommand/mod.rs](pico/sdk/cli/src/subcommand/mod.rs)
- [pico/sdk/cli/src/subcommand/new.rs](pico/sdk/cli/src/subcommand/new.rs)
- [pico/sdk/cli/src/subcommand/prove.rs](pico/sdk/cli/src/subcommand/prove.rs)
- [pico/sdk/patch-libs/Cargo.toml](pico/sdk/patch-libs/Cargo.toml)
- [pico/sdk/patch-libs/src/bls12381.rs](pico/sdk/patch-libs/src/bls12381.rs)
- [pico/sdk/patch-libs/src/bn254.rs](pico/sdk/patch-libs/src/bn254.rs)
- [pico/sdk/patch-libs/src/ed25519.rs](pico/sdk/patch-libs/src/ed25519.rs)
- [pico/sdk/patch-libs/src/io.rs](pico/sdk/patch-libs/src/io.rs)
- [pico/sdk/patch-libs/src/lib.rs](pico/sdk/patch-libs/src/lib.rs)
- [pico/sdk/patch-libs/src/secp256k1.rs](pico/sdk/patch-libs/src/secp256k1.rs)
- [pico/sdk/patch-libs/src/unconstrained.rs](pico/sdk/patch-libs/src/unconstrained.rs)
- [pico/sdk/patch-libs/src/utils.rs](pico/sdk/patch-libs/src/utils.rs)
- [pico/sdk/patch-libs/src/verify.rs](pico/sdk/patch-libs/src/verify.rs)
- [pico/sdk/sdk/Cargo.toml](pico/sdk/sdk/Cargo.toml)
- [pico/sdk/sdk/src/client.rs](pico/sdk/sdk/src/client.rs)
- [pico/sdk/sdk/src/command.rs](pico/sdk/sdk/src/command.rs)
- [pico/sdk/sdk/src/heap.rs](pico/sdk/sdk/src/heap.rs)
- [pico/sdk/sdk/src/io.rs](pico/sdk/sdk/src/io.rs)
- [pico/sdk/sdk/src/libm.rs](pico/sdk/sdk/src/libm.rs)
- [pico/sdk/sdk/src/lib.rs](pico/sdk/sdk/src/lib.rs)
- [pico/sdk/sdk/src/m31_client.rs](pico/sdk/sdk/src/m31_client.rs)
- [pico/sdk/sdk/src/memcpy.s](pico/sdk/sdk/src/memcpy.s)
- [pico/sdk/sdk/src/memset.s](pico/sdk/sdk/src/memset.s)
- [pico/sdk/sdk/src/poseidon2_hash.rs](pico/sdk/sdk/src/poseidon2_hash.rs)
- [pico/sdk/sdk/src/riscv_ecalls/bigint.rs](pico/sdk/sdk/src/riscv_ecalls/bigint.rs)
- [pico/sdk/sdk/src/riscv_ecalls/bls12381.rs](pico/sdk/sdk/src/riscv_ecalls/bls12381.rs)
- [pico/sdk/sdk/src/riscv_ecalls/bn254.rs](pico/sdk/sdk/src/riscv_ecalls/bn254.rs)
- [pico/sdk/sdk/src/riscv_ecalls/ed25519.rs](pico/sdk/sdk/src/riscv_ecalls/ed25519.rs)
- [pico/sdk/sdk/src/riscv_ecalls/fptower.rs](pico/sdk/sdk/src/riscv_ecalls/fptower.rs)
- [pico/sdk/sdk/src/riscv_ecalls/halt.rs](pico/sdk/sdk/src/riscv_ecalls/halt.rs)
- [pico/sdk/sdk/src/riscv_ecalls/io.rs](pico/sdk/sdk/src/riscv_ecalls/io.rs)
- [pico/sdk/sdk/src/riscv_ecalls/keccak_permute.rs](pico/sdk/sdk/src/riscv_ecalls/keccak_permute.rs)
- [pico/sdk/sdk/src/riscv_ecalls/memory.rs](pico/sdk/sdk/src/riscv_ecalls/memory.rs)
- [pico/sdk/sdk/src/riscv_ecalls/mod.rs](pico/sdk/sdk/src/riscv_ecalls/mod.rs)
- [pico/sdk/sdk/src/riscv_ecalls/poseidon2.rs](pico/sdk/sdk/src/riscv_ecalls/poseidon2.rs)
- [pico/sdk/sdk/src/riscv_ecalls/secp256k1.rs](pico/sdk/sdk/src/riscv_ecalls/secp256k1.rs)
- [pico/sdk/sdk/src/riscv_ecalls/sha_compress.rs](pico/sdk/sdk/src/riscv_ecalls/sha_compress.rs)
- [pico/sdk/sdk/src/riscv_ecalls/sha_extend.rs](pico/sdk/sdk/src/riscv_ecalls/sha_extend.rs)
- [pico/sdk/sdk/src/riscv_ecalls/sys.rs](pico/sdk/sdk/src/riscv_ecalls/sys.rs)
- [pico/sdk/sdk/src/riscv_ecalls/uint256_mul.rs](pico/sdk/sdk/src/riscv_ecalls/uint256_mul.rs)
- [pico/sdk/sdk/src/riscv_ecalls/unconstrained.rs](pico/sdk/sdk/src/riscv_ecalls/unconstrained.rs)
- [pico/sdk/sdk/src/riscv_ecalls/verify.rs](pico/sdk/sdk/src/riscv_ecalls/verify.rs)
- [pico/sdk/sdk/src/verify.rs](pico/sdk/sdk/src/verify.rs)
- [pico/vm/src/chips/chips/alu/add_sub/columns.rs](pico/vm/src/chips/chips/alu/add_sub/columns.rs)
- [pico/vm/src/chips/chips/alu/add_sub/constraints.rs](pico/vm/src/chips/chips/alu/add_sub/constraints.rs)
- [pico/vm/src/chips/chips/alu/add_sub/mod.rs](pico/vm/src/chips/chips/alu/add_sub/mod.rs)
- [pico/vm/src/chips/chips/alu/add_sub/traces.rs](pico/vm/src/chips/chips/alu/add_sub/traces.rs)
- [pico/vm/src/chips/chips/alu_base/columns.rs](pico/vm/src/chips/chips/alu_base/columns.rs)
- [pico/vm/src/chips/chips/alu_base/constraints.rs](pico/vm/src/chips/chips/alu_base/constraints.rs)
- [pico/vm/src/chips/chips/alu_base/mod.rs](pico/vm/src/chips/chips/alu_base/mod.rs)
- [pico/vm/src/chips/chips/alu_base/traces.rs](pico/vm/src/chips/chips/alu_base/traces.rs)
- [pico/vm/src/chips/chips/alu/bitwise/columns.rs](pico/vm/src/chips/chips/alu/bitwise/columns.rs)
- [pico/vm/src/chips/chips/alu/bitwise/constraints.rs](pico/vm/src/chips/chips/alu/bitwise/constraints.rs)
- [pico/vm/src/chips/chips/alu/bitwise/mod.rs](pico/vm/src/chips/chips/alu/bitwise/mod.rs)
- [pico/vm/src/chips/chips/alu/bitwise/traces.rs](pico/vm/src/chips/chips/alu/bitwise/traces.rs)
- [pico/vm/src/chips/chips/alu/divrem/columns.rs](pico/vm/src/chips/chips/alu/divrem/columns.rs)
- [pico/vm/src/chips/chips/alu/divrem/constraints.rs](pico/vm/src/chips/chips/alu/divrem/constraints.rs)
- [pico/vm/src/chips/chips/alu/divrem/mod.rs](pico/vm/src/chips/chips/alu/divrem/mod.rs)
- [pico/vm/src/chips/chips/alu/divrem/traces.rs](pico/vm/src/chips/chips/alu/divrem/traces.rs)
- [pico/vm/src/chips/chips/alu/divrem/utils.rs](pico/vm/src/chips/chips/alu/divrem/utils.rs)
- [pico/vm/src/chips/chips/alu/event.rs](pico/vm/src/chips/chips/alu/event.rs)
- [pico/vm/src/chips/chips/alu_ext/columns.rs](pico/vm/src/chips/chips/alu_ext/columns.rs)
- [pico/vm/src/chips/chips/alu_ext/constraints.rs](pico/vm/src/chips/chips/alu_ext/constraints.rs)
- [pico/vm/src/chips/chips/alu_ext/mod.rs](pico/vm/src/chips/chips/alu_ext/mod.rs)
- [pico/vm/src/chips/chips/alu_ext/traces.rs](pico/vm/src/chips/chips/alu_ext/traces.rs)
- [pico/vm/src/chips/chips/alu/lt/columns.rs](pico/vm/src/chips/chips/alu/lt/columns.rs)
- [pico/vm/src/chips/chips/alu/lt/constraints.rs](pico/vm/src/chips/chips/alu/lt/constraints.rs)
- [pico/vm/src/chips/chips/alu/lt/mod.rs](pico/vm/src/chips/chips/alu/lt/mod.rs)
- [pico/vm/src/chips/chips/alu/lt/traces.rs](pico/vm/src/chips/chips/alu/lt/traces.rs)
- [pico/vm/src/chips/chips/alu/mod.rs](pico/vm/src/chips/chips/alu/mod.rs)
- [pico/vm/src/chips/chips/alu/mul/columns.rs](pico/vm/src/chips/chips/alu/mul/columns.rs)
- [pico/vm/src/chips/chips/alu/mul/constraints.rs](pico/vm/src/chips/chips/alu/mul/constraints.rs)
- [pico/vm/src/chips/chips/alu/mul/mod.rs](pico/vm/src/chips/chips/alu/mul/mod.rs)
- [pico/vm/src/chips/chips/alu/mul/traces.rs](pico/vm/src/chips/chips/alu/mul/traces.rs)
- [pico/vm/src/chips/chips/alu/sll/columns.rs](pico/vm/src/chips/chips/alu/sll/columns.rs)
- [pico/vm/src/chips/chips/alu/sll/constraints.rs](pico/vm/src/chips/chips/alu/sll/constraints.rs)
- [pico/vm/src/chips/chips/alu/sll/mod.rs](pico/vm/src/chips/chips/alu/sll/mod.rs)
- [pico/vm/src/chips/chips/alu/sll/traces.rs](pico/vm/src/chips/chips/alu/sll/traces.rs)
- [pico/vm/src/chips/chips/alu/sr/columns.rs](pico/vm/src/chips/chips/alu/sr/columns.rs)
- [pico/vm/src/chips/chips/alu/sr/constraints.rs](pico/vm/src/chips/chips/alu/sr/constraints.rs)
- [pico/vm/src/chips/chips/alu/sr/mod.rs](pico/vm/src/chips/chips/alu/sr/mod.rs)
- [pico/vm/src/chips/chips/alu/sr/traces.rs](pico/vm/src/chips/chips/alu/sr/traces.rs)
- [pico/vm/src/chips/chips/batch_fri/columns.rs](pico/vm/src/chips/chips/batch_fri/columns.rs)
- [pico/vm/src/chips/chips/batch_fri/constraints.rs](pico/vm/src/chips/chips/batch_fri/constraints.rs)
- [pico/vm/src/chips/chips/batch_fri/mod.rs](pico/vm/src/chips/chips/batch_fri/mod.rs)
- [pico/vm/src/chips/chips/batch_fri/traces.rs](pico/vm/src/chips/chips/batch_fri/traces.rs)
- [pico/vm/src/chips/chips/byte/columns.rs](pico/vm/src/chips/chips/byte/columns.rs)
- [pico/vm/src/chips/chips/byte/constraints.rs](pico/vm/src/chips/chips/byte/constraints.rs)
- [pico/vm/src/chips/chips/byte/event.rs](pico/vm/src/chips/chips/byte/event.rs)
- [pico/vm/src/chips/chips/byte/mod.rs](pico/vm/src/chips/chips/byte/mod.rs)
- [pico/vm/src/chips/chips/byte/traces.rs](pico/vm/src/chips/chips/byte/traces.rs)
- [pico/vm/src/chips/chips/byte/utils.rs](pico/vm/src/chips/chips/byte/utils.rs)
- [pico/vm/src/chips/chips/events.rs](pico/vm/src/chips/chips/events.rs)
- [pico/vm/src/chips/chips/exp_reverse_bits/columns.rs](pico/vm/src/chips/chips/exp_reverse_bits/columns.rs)
- [pico/vm/src/chips/chips/exp_reverse_bits/constraints.rs](pico/vm/src/chips/chips/exp_reverse_bits/constraints.rs)
- [pico/vm/src/chips/chips/exp_reverse_bits/mod.rs](pico/vm/src/chips/chips/exp_reverse_bits/mod.rs)
- [pico/vm/src/chips/chips/exp_reverse_bits/traces.rs](pico/vm/src/chips/chips/exp_reverse_bits/traces.rs)
- [pico/vm/src/chips/chips/mod.rs](pico/vm/src/chips/chips/mod.rs)
- [pico/vm/src/chips/chips/poseidon2/constraints.rs](pico/vm/src/chips/chips/poseidon2/constraints.rs)
- [pico/vm/src/chips/chips/poseidon2/mod.rs](pico/vm/src/chips/chips/poseidon2/mod.rs)
- [pico/vm/src/chips/chips/poseidon2/traces.rs](pico/vm/src/chips/chips/poseidon2/traces.rs)
- [pico/vm/src/chips/chips/public_values/columns.rs](pico/vm/src/chips/chips/public_values/columns.rs)
- [pico/vm/src/chips/chips/public_values/constraints.rs](pico/vm/src/chips/chips/public_values/constraints.rs)
- [pico/vm/src/chips/chips/public_values/mod.rs](pico/vm/src/chips/chips/public_values/mod.rs)
- [pico/vm/src/chips/chips/public_values/traces.rs](pico/vm/src/chips/chips/public_values/traces.rs)
- [pico/vm/src/chips/chips/recursion_memory/constant/columns.rs](pico/vm/src/chips/chips/recursion_memory/constant/columns.rs)
- [pico/vm/src/chips/chips/recursion_memory/constant/constraints.rs](pico/vm/src/chips/chips/recursion_memory/constant/constraints.rs)
- [pico/vm/src/chips/chips/recursion_memory/constant/mod.rs](pico/vm/src/chips/chips/recursion_memory/constant/mod.rs)
- [pico/vm/src/chips/chips/recursion_memory/constant/traces.rs](pico/vm/src/chips/chips/recursion_memory/constant/traces.rs)
- [pico/vm/src/chips/chips/recursion_memory/mod.rs](pico/vm/src/chips/chips/recursion_memory/mod.rs)
- [pico/vm/src/chips/chips/recursion_memory/variable/columns.rs](pico/vm/src/chips/chips/recursion_memory/variable/columns.rs)
- [pico/vm/src/chips/chips/recursion_memory/variable/constraints.rs](pico/vm/src/chips/chips/recursion_memory/variable/constraints.rs)
- [pico/vm/src/chips/chips/recursion_memory/variable/mod.rs](pico/vm/src/chips/chips/recursion_memory/variable/mod.rs)
- [pico/vm/src/chips/chips/recursion_memory/variable/traces.rs](pico/vm/src/chips/chips/recursion_memory/variable/traces.rs)
- [pico/vm/src/chips/chips/riscv_cpu/auipc/columns.rs](pico/vm/src/chips/chips/riscv_cpu/auipc/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/auipc/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/auipc/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/auipc/mod.rs](pico/vm/src/chips/chips/riscv_cpu/auipc/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/auipc/traces.rs](pico/vm/src/chips/chips/riscv_cpu/auipc/traces.rs)
- [pico/vm/src/chips/chips/riscv_cpu/branch/columns.rs](pico/vm/src/chips/chips/riscv_cpu/branch/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/branch/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/branch/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/branch/mod.rs](pico/vm/src/chips/chips/riscv_cpu/branch/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/branch/traces.rs](pico/vm/src/chips/chips/riscv_cpu/branch/traces.rs)
- [pico/vm/src/chips/chips/riscv_cpu/chunk_clk/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/chunk_clk/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/chunk_clk/mod.rs](pico/vm/src/chips/chips/riscv_cpu/chunk_clk/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/chunk_clk/traces.rs](pico/vm/src/chips/chips/riscv_cpu/chunk_clk/traces.rs)
- [pico/vm/src/chips/chips/riscv_cpu/columns.rs](pico/vm/src/chips/chips/riscv_cpu/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/ecall/columns.rs](pico/vm/src/chips/chips/riscv_cpu/ecall/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/ecall/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/ecall/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/ecall/mod.rs](pico/vm/src/chips/chips/riscv_cpu/ecall/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/ecall/traces.rs](pico/vm/src/chips/chips/riscv_cpu/ecall/traces.rs)
- [pico/vm/src/chips/chips/riscv_cpu/event.rs](pico/vm/src/chips/chips/riscv_cpu/event.rs)
- [pico/vm/src/chips/chips/riscv_cpu/instruction/columns.rs](pico/vm/src/chips/chips/riscv_cpu/instruction/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/instruction/mod.rs](pico/vm/src/chips/chips/riscv_cpu/instruction/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/jump/columns.rs](pico/vm/src/chips/chips/riscv_cpu/jump/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/jump/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/jump/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/jump/mod.rs](pico/vm/src/chips/chips/riscv_cpu/jump/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/jump/traces.rs](pico/vm/src/chips/chips/riscv_cpu/jump/traces.rs)
- [pico/vm/src/chips/chips/riscv_cpu/mod.rs](pico/vm/src/chips/chips/riscv_cpu/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/opcode_selector/columns.rs](pico/vm/src/chips/chips/riscv_cpu/opcode_selector/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/opcode_selector/mod.rs](pico/vm/src/chips/chips/riscv_cpu/opcode_selector/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/opcode_specific/columns.rs](pico/vm/src/chips/chips/riscv_cpu/opcode_specific/columns.rs)
- [pico/vm/src/chips/chips/riscv_cpu/opcode_specific/mod.rs](pico/vm/src/chips/chips/riscv_cpu/opcode_specific/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/public_values/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/public_values/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/public_values/mod.rs](pico/vm/src/chips/chips/riscv_cpu/public_values/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/register/constraints.rs](pico/vm/src/chips/chips/riscv_cpu/register/constraints.rs)
- [pico/vm/src/chips/chips/riscv_cpu/register/mod.rs](pico/vm/src/chips/chips/riscv_cpu/register/mod.rs)
- [pico/vm/src/chips/chips/riscv_cpu/traces.rs](pico/vm/src/chips/chips/riscv_cpu/traces.rs)
- [pico/vm/src/chips/chips/riscv_cpu/utils.rs](pico/vm/src/chips/chips/riscv_cpu/utils.rs)
- [pico/vm/src/chips/chips/riscv_global/columns.rs](pico/vm/src/chips/chips/riscv_global/columns.rs)
- [pico/vm/src/chips/chips/riscv_global/constraints.rs](pico/vm/src/chips/chips/riscv_global/constraints.rs)
- [pico/vm/src/chips/chips/riscv_global/event.rs](pico/vm/src/chips/chips/riscv_global/event.rs)
- [pico/vm/src/chips/chips/riscv_global/mod.rs](pico/vm/src/chips/chips/riscv_global/mod.rs)
- [pico/vm/src/chips/chips/riscv_global/traces.rs](pico/vm/src/chips/chips/riscv_global/traces.rs)
- [pico/vm/src/chips/chips/riscv_memory/event.rs](pico/vm/src/chips/chips/riscv_memory/event.rs)
- [pico/vm/src/chips/chips/riscv_memory/initialize_finalize/columns.rs](pico/vm/src/chips/chips/riscv_memory/initialize_finalize/columns.rs)
- [pico/vm/src/chips/chips/riscv_memory/initialize_finalize/constraints.rs](pico/vm/src/chips/chips/riscv_memory/initialize_finalize/constraints.rs)
- [pico/vm/src/chips/chips/riscv_memory/initialize_finalize/mod.rs](pico/vm/src/chips/chips/riscv_memory/initialize_finalize/mod.rs)
- [pico/vm/src/chips/chips/riscv_memory/initialize_finalize/traces.rs](pico/vm/src/chips/chips/riscv_memory/initialize_finalize/traces.rs)
- [pico/vm/src/chips/chips/riscv_memory/local/columns.rs](pico/vm/src/chips/chips/riscv_memory/local/columns.rs)
- [pico/vm/src/chips/chips/riscv_memory/local/constraints.rs](pico/vm/src/chips/chips/riscv_memory/local/constraints.rs)
- [pico/vm/src/chips/chips/riscv_memory/local/mod.rs](pico/vm/src/chips/chips/riscv_memory/local/mod.rs)
- [pico/vm/src/chips/chips/riscv_memory/local/traces.rs](pico/vm/src/chips/chips/riscv_memory/local/traces.rs)
- [pico/vm/src/chips/chips/riscv_memory/mod.rs](pico/vm/src/chips/chips/riscv_memory/mod.rs)
- [pico/vm/src/chips/chips/riscv_memory/read_write/columns.rs](pico/vm/src/chips/chips/riscv_memory/read_write/columns.rs)
- [pico/vm/src/chips/chips/riscv_memory/read_write/constraints.rs](pico/vm/src/chips/chips/riscv_memory/read_write/constraints.rs)
- [pico/vm/src/chips/chips/riscv_memory/read_write/mod.rs](pico/vm/src/chips/chips/riscv_memory/read_write/mod.rs)
- [pico/vm/src/chips/chips/riscv_memory/read_write/traces.rs](pico/vm/src/chips/chips/riscv_memory/read_write/traces.rs)
- [pico/vm/src/chips/chips/riscv_poseidon2/constraints.rs](pico/vm/src/chips/chips/riscv_poseidon2/constraints.rs)
- [pico/vm/src/chips/chips/riscv_poseidon2/event.rs](pico/vm/src/chips/chips/riscv_poseidon2/event.rs)
- [pico/vm/src/chips/chips/riscv_poseidon2/mod.rs](pico/vm/src/chips/chips/riscv_poseidon2/mod.rs)
- [pico/vm/src/chips/chips/riscv_poseidon2/traces.rs](pico/vm/src/chips/chips/riscv_poseidon2/traces.rs)
- [pico/vm/src/chips/chips/riscv_program/columns.rs](pico/vm/src/chips/chips/riscv_program/columns.rs)
- [pico/vm/src/chips/chips/riscv_program/constraints.rs](pico/vm/src/chips/chips/riscv_program/constraints.rs)
- [pico/vm/src/chips/chips/riscv_program/mod.rs](pico/vm/src/chips/chips/riscv_program/mod.rs)
- [pico/vm/src/chips/chips/riscv_program/traces.rs](pico/vm/src/chips/chips/riscv_program/traces.rs)
- [pico/vm/src/chips/chips/select/columns.rs](pico/vm/src/chips/chips/select/columns.rs)
- [pico/vm/src/chips/chips/select/constraints.rs](pico/vm/src/chips/chips/select/constraints.rs)
- [pico/vm/src/chips/chips/select/mod.rs](pico/vm/src/chips/chips/select/mod.rs)
- [pico/vm/src/chips/chips/select/trace.rs](pico/vm/src/chips/chips/select/trace.rs)
- [pico/vm/src/chips/chips/syscall/columns.rs](pico/vm/src/chips/chips/syscall/columns.rs)
- [pico/vm/src/chips/chips/syscall/constraints.rs](pico/vm/src/chips/chips/syscall/constraints.rs)
- [pico/vm/src/chips/chips/syscall/mod.rs](pico/vm/src/chips/chips/syscall/mod.rs)
- [pico/vm/src/chips/chips/syscall/traces.rs](pico/vm/src/chips/chips/syscall/traces.rs)
- [pico/vm/src/chips/chips/toys/lookup_toy.rs](pico/vm/src/chips/chips/toys/lookup_toy.rs)
- [pico/vm/src/chips/chips/toys/mod.rs](pico/vm/src/chips/chips/toys/mod.rs)
- [pico/vm/src/chips/chips/toys/toy.rs](pico/vm/src/chips/chips/toys/toy.rs)
- [pico/vm/src/chips/gadgets/add4.rs](pico/vm/src/chips/gadgets/add4.rs)
- [pico/vm/src/chips/gadgets/add5.rs](pico/vm/src/chips/gadgets/add5.rs)
- [pico/vm/src/chips/gadgets/add.rs](pico/vm/src/chips/gadgets/add.rs)
- [pico/vm/src/chips/gadgets/and.rs](pico/vm/src/chips/gadgets/and.rs)
- [pico/vm/src/chips/gadgets/curves/edwards/ed25519.rs](pico/vm/src/chips/gadgets/curves/edwards/ed25519.rs)
- [pico/vm/src/chips/gadgets/curves/edwards/mod.rs](pico/vm/src/chips/gadgets/curves/edwards/mod.rs)
- [pico/vm/src/chips/gadgets/curves/mod.rs](pico/vm/src/chips/gadgets/curves/mod.rs)
- [pico/vm/src/chips/gadgets/curves/scalar_mul.rs](pico/vm/src/chips/gadgets/curves/scalar_mul.rs)
- [pico/vm/src/chips/gadgets/curves/weierstrass/bls381.rs](pico/vm/src/chips/gadgets/curves/weierstrass/bls381.rs)
- [pico/vm/src/chips/gadgets/curves/weierstrass/bn254.rs](pico/vm/src/chips/gadgets/curves/weierstrass/bn254.rs)
- [pico/vm/src/chips/gadgets/curves/weierstrass/mod.rs](pico/vm/src/chips/gadgets/curves/weierstrass/mod.rs)
- [pico/vm/src/chips/gadgets/curves/weierstrass/secp256k1.rs](pico/vm/src/chips/gadgets/curves/weierstrass/secp256k1.rs)
- [pico/vm/src/chips/gadgets/field/bls381.rs](pico/vm/src/chips/gadgets/field/bls381.rs)
- [pico/vm/src/chips/gadgets/field/bn254.rs](pico/vm/src/chips/gadgets/field/bn254.rs)
- [pico/vm/src/chips/gadgets/field/field_den.rs](pico/vm/src/chips/gadgets/field/field_den.rs)
- [pico/vm/src/chips/gadgets/field/field_inner_product.rs](pico/vm/src/chips/gadgets/field/field_inner_product.rs)
- [pico/vm/src/chips/gadgets/field/field_lt.rs](pico/vm/src/chips/gadgets/field/field_lt.rs)
- [pico/vm/src/chips/gadgets/field/field_op.rs](pico/vm/src/chips/gadgets/field/field_op.rs)
- [pico/vm/src/chips/gadgets/field/field_sqrt.rs](pico/vm/src/chips/gadgets/field/field_sqrt.rs)
- [pico/vm/src/chips/gadgets/field/mod.rs](pico/vm/src/chips/gadgets/field/mod.rs)
- [pico/vm/src/chips/gadgets/field_range_check/bit_decomposition.rs](pico/vm/src/chips/gadgets/field_range_check/bit_decomposition.rs)
- [pico/vm/src/chips/gadgets/field_range_check/mod.rs](pico/vm/src/chips/gadgets/field_range_check/mod.rs)
- [pico/vm/src/chips/gadgets/field_range_check/word_range.rs](pico/vm/src/chips/gadgets/field_range_check/word_range.rs)
- [pico/vm/src/chips/gadgets/field/secp256k1.rs](pico/vm/src/chips/gadgets/field/secp256k1.rs)
- [pico/vm/src/chips/gadgets/field/utils.rs](pico/vm/src/chips/gadgets/field/utils.rs)
- [pico/vm/src/chips/gadgets/fixed_rotate_right.rs](pico/vm/src/chips/gadgets/fixed_rotate_right.rs)
- [pico/vm/src/chips/gadgets/fixed_shift_right.rs](pico/vm/src/chips/gadgets/fixed_shift_right.rs)
- [pico/vm/src/chips/gadgets/global_accumulation.rs](pico/vm/src/chips/gadgets/global_accumulation.rs)
- [pico/vm/src/chips/gadgets/global_interaction.rs](pico/vm/src/chips/gadgets/global_interaction.rs)
- [pico/vm/src/chips/gadgets/is_equal_word.rs](pico/vm/src/chips/gadgets/is_equal_word.rs)
- [pico/vm/src/chips/gadgets/is_zero.rs](pico/vm/src/chips/gadgets/is_zero.rs)
- [pico/vm/src/chips/gadgets/is_zero_word.rs](pico/vm/src/chips/gadgets/is_zero_word.rs)
- [pico/vm/src/chips/gadgets/lt.rs](pico/vm/src/chips/gadgets/lt.rs)
- [pico/vm/src/chips/gadgets/mod.rs](pico/vm/src/chips/gadgets/mod.rs)
- [pico/vm/src/chips/gadgets/not.rs](pico/vm/src/chips/gadgets/not.rs)
- [pico/vm/src/chips/gadgets/poseidon2/columns.rs](pico/vm/src/chips/gadgets/poseidon2/columns.rs)
- [pico/vm/src/chips/gadgets/poseidon2/constants.rs](pico/vm/src/chips/gadgets/poseidon2/constants.rs)
- [pico/vm/src/chips/gadgets/poseidon2/constraints.rs](pico/vm/src/chips/gadgets/poseidon2/constraints.rs)
- [pico/vm/src/chips/gadgets/poseidon2/mod.rs](pico/vm/src/chips/gadgets/poseidon2/mod.rs)
- [pico/vm/src/chips/gadgets/poseidon2/traces.rs](pico/vm/src/chips/gadgets/poseidon2/traces.rs)
- [pico/vm/src/chips/gadgets/poseidon2/utils.rs](pico/vm/src/chips/gadgets/poseidon2/utils.rs)
- [pico/vm/src/chips/gadgets/uint256/mod.rs](pico/vm/src/chips/gadgets/uint256/mod.rs)
- [pico/vm/src/chips/gadgets/utils/conversions.rs](pico/vm/src/chips/gadgets/utils/conversions.rs)
- [pico/vm/src/chips/gadgets/utils/field_params.rs](pico/vm/src/chips/gadgets/utils/field_params.rs)
- [pico/vm/src/chips/gadgets/utils/limbs.rs](pico/vm/src/chips/gadgets/utils/limbs.rs)
- [pico/vm/src/chips/gadgets/utils/mod.rs](pico/vm/src/chips/gadgets/utils/mod.rs)
- [pico/vm/src/chips/gadgets/utils/polynomial.rs](pico/vm/src/chips/gadgets/utils/polynomial.rs)
- [pico/vm/src/chips/gadgets/xor.rs](pico/vm/src/chips/gadgets/xor.rs)
- [pico/vm/src/chips/mod.rs](pico/vm/src/chips/mod.rs)
- [pico/vm/src/chips/precompiles/edwards/ed_add.rs](pico/vm/src/chips/precompiles/edwards/ed_add.rs)
- [pico/vm/src/chips/precompiles/edwards/ed_decompress.rs](pico/vm/src/chips/precompiles/edwards/ed_decompress.rs)
- [pico/vm/src/chips/precompiles/edwards/mod.rs](pico/vm/src/chips/precompiles/edwards/mod.rs)
- [pico/vm/src/chips/precompiles/fptower/fp2_addsub.rs](pico/vm/src/chips/precompiles/fptower/fp2_addsub.rs)
- [pico/vm/src/chips/precompiles/fptower/fp2_mul.rs](pico/vm/src/chips/precompiles/fptower/fp2_mul.rs)
- [pico/vm/src/chips/precompiles/fptower/fp.rs](pico/vm/src/chips/precompiles/fptower/fp.rs)
- [pico/vm/src/chips/precompiles/fptower/mod.rs](pico/vm/src/chips/precompiles/fptower/mod.rs)
- [pico/vm/src/chips/precompiles/keccak256/columns.rs](pico/vm/src/chips/precompiles/keccak256/columns.rs)
- [pico/vm/src/chips/precompiles/keccak256/constraint.rs](pico/vm/src/chips/precompiles/keccak256/constraint.rs)
- [pico/vm/src/chips/precompiles/keccak256/mod.rs](pico/vm/src/chips/precompiles/keccak256/mod.rs)
- [pico/vm/src/chips/precompiles/keccak256/traces.rs](pico/vm/src/chips/precompiles/keccak256/traces.rs)
- [pico/vm/src/chips/precompiles/mod.rs](pico/vm/src/chips/precompiles/mod.rs)
- [pico/vm/src/chips/precompiles/poseidon2/columns.rs](pico/vm/src/chips/precompiles/poseidon2/columns.rs)
- [pico/vm/src/chips/precompiles/poseidon2/constraints.rs](pico/vm/src/chips/precompiles/poseidon2/constraints.rs)
- [pico/vm/src/chips/precompiles/poseidon2/mod.rs](pico/vm/src/chips/precompiles/poseidon2/mod.rs)
- [pico/vm/src/chips/precompiles/poseidon2/traces.rs](pico/vm/src/chips/precompiles/poseidon2/traces.rs)
- [pico/vm/src/chips/precompiles/sha256/compress/columns.rs](pico/vm/src/chips/precompiles/sha256/compress/columns.rs)
- [pico/vm/src/chips/precompiles/sha256/compress/constraints.rs](pico/vm/src/chips/precompiles/sha256/compress/constraints.rs)
- [pico/vm/src/chips/precompiles/sha256/compress/mod.rs](pico/vm/src/chips/precompiles/sha256/compress/mod.rs)
- [pico/vm/src/chips/precompiles/sha256/compress/trace.rs](pico/vm/src/chips/precompiles/sha256/compress/trace.rs)
- [pico/vm/src/chips/precompiles/sha256/extend/columns.rs](pico/vm/src/chips/precompiles/sha256/extend/columns.rs)
- [pico/vm/src/chips/precompiles/sha256/extend/constraints.rs](pico/vm/src/chips/precompiles/sha256/extend/constraints.rs)
- [pico/vm/src/chips/precompiles/sha256/extend/flags.rs](pico/vm/src/chips/precompiles/sha256/extend/flags.rs)
- [pico/vm/src/chips/precompiles/sha256/extend/mod.rs](pico/vm/src/chips/precompiles/sha256/extend/mod.rs)
- [pico/vm/src/chips/precompiles/sha256/extend/trace.rs](pico/vm/src/chips/precompiles/sha256/extend/trace.rs)
- [pico/vm/src/chips/precompiles/sha256/mod.rs](pico/vm/src/chips/precompiles/sha256/mod.rs)
- [pico/vm/src/chips/precompiles/uint256/columns.rs](pico/vm/src/chips/precompiles/uint256/columns.rs)
- [pico/vm/src/chips/precompiles/uint256/constraints.rs](pico/vm/src/chips/precompiles/uint256/constraints.rs)
- [pico/vm/src/chips/precompiles/uint256/mod.rs](pico/vm/src/chips/precompiles/uint256/mod.rs)
- [pico/vm/src/chips/precompiles/uint256/traces.rs](pico/vm/src/chips/precompiles/uint256/traces.rs)
- [pico/vm/src/chips/precompiles/weierstrass/mod.rs](pico/vm/src/chips/precompiles/weierstrass/mod.rs)
- [pico/vm/src/chips/precompiles/weierstrass/weierstrass_add.rs](pico/vm/src/chips/precompiles/weierstrass/weierstrass_add.rs)
- [pico/vm/src/chips/precompiles/weierstrass/weierstrass_decompress.rs](pico/vm/src/chips/precompiles/weierstrass/weierstrass_decompress.rs)
- [pico/vm/src/chips/precompiles/weierstrass/weierstrass_double.rs](pico/vm/src/chips/precompiles/weierstrass/weierstrass_double.rs)
- [pico/vm/src/chips/trace.rs](pico/vm/src/chips/trace.rs)
- [pico/vm/src/chips/utils.rs](pico/vm/src/chips/utils.rs)
- [pico/vm/src/compiler/mod.rs](pico/vm/src/compiler/mod.rs)
- [pico/vm/src/compiler/program.rs](pico/vm/src/compiler/program.rs)
- [pico/vm/src/compiler/recursion/circuit/builder.rs](pico/vm/src/compiler/recursion/circuit/builder.rs)
- [pico/vm/src/compiler/recursion/circuit/challenger.rs](pico/vm/src/compiler/recursion/circuit/challenger.rs)
- [pico/vm/src/compiler/recursion/circuit/config.rs](pico/vm/src/compiler/recursion/circuit/config.rs)
- [pico/vm/src/compiler/recursion/circuit/constraints.rs](pico/vm/src/compiler/recursion/circuit/constraints.rs)
- [pico/vm/src/compiler/recursion/circuit/domain.rs](pico/vm/src/compiler/recursion/circuit/domain.rs)
- [pico/vm/src/compiler/recursion/circuit/fri.rs](pico/vm/src/compiler/recursion/circuit/fri.rs)
- [pico/vm/src/compiler/recursion/circuit/hash.rs](pico/vm/src/compiler/recursion/circuit/hash.rs)
- [pico/vm/src/compiler/recursion/circuit/merkle_tree.rs](pico/vm/src/compiler/recursion/circuit/merkle_tree.rs)
- [pico/vm/src/compiler/recursion/circuit/mod.rs](pico/vm/src/compiler/recursion/circuit/mod.rs)
- [pico/vm/src/compiler/recursion/circuit/stark.rs](pico/vm/src/compiler/recursion/circuit/stark.rs)
- [pico/vm/src/compiler/recursion/circuit/types.rs](pico/vm/src/compiler/recursion/circuit/types.rs)
- [pico/vm/src/compiler/recursion/circuit/utils.rs](pico/vm/src/compiler/recursion/circuit/utils.rs)
- [pico/vm/src/compiler/recursion/circuit/witness/embed.rs](pico/vm/src/compiler/recursion/circuit/witness/embed.rs)
- [pico/vm/src/compiler/recursion/circuit/witness/mod.rs](pico/vm/src/compiler/recursion/circuit/witness/mod.rs)
- [pico/vm/src/compiler/recursion/circuit/witness/stark.rs](pico/vm/src/compiler/recursion/circuit/witness/stark.rs)
- [pico/vm/src/compiler/recursion/circuit/witness/witnessable.rs](pico/vm/src/compiler/recursion/circuit/witness/witnessable.rs)
- [pico/vm/src/compiler/recursion/constraints/mod.rs](pico/vm/src/compiler/recursion/constraints/mod.rs)
- [pico/vm/src/compiler/recursion/constraints/opcodes.rs](pico/vm/src/compiler/recursion/constraints/opcodes.rs)
- [pico/vm/src/compiler/recursion/instruction.rs](pico/vm/src/compiler/recursion/instruction.rs)
- [pico/vm/src/compiler/recursion/ir/arithmetic.rs](pico/vm/src/compiler/recursion/ir/arithmetic.rs)
- [pico/vm/src/compiler/recursion/ir/bits.rs](pico/vm/src/compiler/recursion/ir/bits.rs)
- [pico/vm/src/compiler/recursion/ir/block.rs](pico/vm/src/compiler/recursion/ir/block.rs)
- [pico/vm/src/compiler/recursion/ir/builder.rs](pico/vm/src/compiler/recursion/ir/builder.rs)
- [pico/vm/src/compiler/recursion/ir/collections.rs](pico/vm/src/compiler/recursion/ir/collections.rs)
- [pico/vm/src/compiler/recursion/ir/compiler.rs](pico/vm/src/compiler/recursion/ir/compiler.rs)
- [pico/vm/src/compiler/recursion/ir/instructions.rs](pico/vm/src/compiler/recursion/ir/instructions.rs)
- [pico/vm/src/compiler/recursion/ir/mod.rs](pico/vm/src/compiler/recursion/ir/mod.rs)
- [pico/vm/src/compiler/recursion/ir/ptr.rs](pico/vm/src/compiler/recursion/ir/ptr.rs)
- [pico/vm/src/compiler/recursion/ir/symbolic.rs](pico/vm/src/compiler/recursion/ir/symbolic.rs)
- [pico/vm/src/compiler/recursion/ir/types.rs](pico/vm/src/compiler/recursion/ir/types.rs)
- [pico/vm/src/compiler/recursion/ir/utils.rs](pico/vm/src/compiler/recursion/ir/utils.rs)
- [pico/vm/src/compiler/recursion/ir/var.rs](pico/vm/src/compiler/recursion/ir/var.rs)
- [pico/vm/src/compiler/recursion/mod.rs](pico/vm/src/compiler/recursion/mod.rs)
- [pico/vm/src/compiler/recursion/program.rs](pico/vm/src/compiler/recursion/program.rs)
- [pico/vm/src/compiler/recursion/types.rs](pico/vm/src/compiler/recursion/types.rs)
- [pico/vm/src/compiler/riscv/compiler.rs](pico/vm/src/compiler/riscv/compiler.rs)
- [pico/vm/src/compiler/riscv/disassembler/elf.rs](pico/vm/src/compiler/riscv/disassembler/elf.rs)
- [pico/vm/src/compiler/riscv/disassembler/mod.rs](pico/vm/src/compiler/riscv/disassembler/mod.rs)
- [pico/vm/src/compiler/riscv/disassembler/rrs.rs](pico/vm/src/compiler/riscv/disassembler/rrs.rs)
- [pico/vm/src/compiler/riscv/instruction.rs](pico/vm/src/compiler/riscv/instruction.rs)
- [pico/vm/src/compiler/riscv/mod.rs](pico/vm/src/compiler/riscv/mod.rs)
- [pico/vm/src/compiler/riscv/opcode.rs](pico/vm/src/compiler/riscv/opcode.rs)
- [pico/vm/src/compiler/riscv/program.rs](pico/vm/src/compiler/riscv/program.rs)
- [pico/vm/src/compiler/riscv/register.rs](pico/vm/src/compiler/riscv/register.rs)
- [pico/vm/src/compiler/word.rs](pico/vm/src/compiler/word.rs)
- [pico/vm/src/configs/config.rs](pico/vm/src/configs/config.rs)
- [pico/vm/src/configs/field_config/bb_bn254.rs](pico/vm/src/configs/field_config/bb_bn254.rs)
- [pico/vm/src/configs/field_config/bb_simple.rs](pico/vm/src/configs/field_config/bb_simple.rs)
- [pico/vm/src/configs/field_config/kb_bn254.rs](pico/vm/src/configs/field_config/kb_bn254.rs)
- [pico/vm/src/configs/field_config/kb_simple.rs](pico/vm/src/configs/field_config/kb_simple.rs)
- [pico/vm/src/configs/field_config/mod.rs](pico/vm/src/configs/field_config/mod.rs)
- [pico/vm/src/configs/mod.rs](pico/vm/src/configs/mod.rs)
- [pico/vm/src/configs/stark_config/bb_bn254_poseidon2.rs](pico/vm/src/configs/stark_config/bb_bn254_poseidon2.rs)
- [pico/vm/src/configs/stark_config/bb_poseidon2.rs](pico/vm/src/configs/stark_config/bb_poseidon2.rs)
- [pico/vm/src/configs/stark_config/kb_bn254_poseidon2.rs](pico/vm/src/configs/stark_config/kb_bn254_poseidon2.rs)
- [pico/vm/src/configs/stark_config/kb_poseidon2.rs](pico/vm/src/configs/stark_config/kb_poseidon2.rs)
- [pico/vm/src/configs/stark_config/m31_poseidon2.rs](pico/vm/src/configs/stark_config/m31_poseidon2.rs)
- [pico/vm/src/configs/stark_config/mod.rs](pico/vm/src/configs/stark_config/mod.rs)
- [pico/vm/src/emulator/emulator.rs](pico/vm/src/emulator/emulator.rs)
- [pico/vm/src/emulator/mod.rs](pico/vm/src/emulator/mod.rs)
- [pico/vm/src/emulator/opts.rs](pico/vm/src/emulator/opts.rs)
- [pico/vm/src/emulator/record.rs](pico/vm/src/emulator/record.rs)
- [pico/vm/src/emulator/recursion/emulator/memory.rs](pico/vm/src/emulator/recursion/emulator/memory.rs)
- [pico/vm/src/emulator/recursion/emulator/mod.rs](pico/vm/src/emulator/recursion/emulator/mod.rs)
- [pico/vm/src/emulator/recursion/emulator/opcode.rs](pico/vm/src/emulator/recursion/emulator/opcode.rs)
- [pico/vm/src/emulator/recursion/mod.rs](pico/vm/src/emulator/recursion/mod.rs)
- [pico/vm/src/emulator/recursion/public_values.rs](pico/vm/src/emulator/recursion/public_values.rs)
- [pico/vm/src/emulator/recursion/record.rs](pico/vm/src/emulator/recursion/record.rs)
- [pico/vm/src/emulator/riscv/emulator/instruction.rs](pico/vm/src/emulator/riscv/emulator/instruction.rs)
- [pico/vm/src/emulator/riscv/emulator/instruction_simple.rs](pico/vm/src/emulator/riscv/emulator/instruction_simple.rs)
- [pico/vm/src/emulator/riscv/emulator/mode.rs](pico/vm/src/emulator/riscv/emulator/mode.rs)
- [pico/vm/src/emulator/riscv/emulator/mod.rs](pico/vm/src/emulator/riscv/emulator/mod.rs)
- [pico/vm/src/emulator/riscv/emulator/unconstrained.rs](pico/vm/src/emulator/riscv/emulator/unconstrained.rs)
- [pico/vm/src/emulator/riscv/emulator/util.rs](pico/vm/src/emulator/riscv/emulator/util.rs)
- [pico/vm/src/emulator/riscv/hook/ecrecover.rs](pico/vm/src/emulator/riscv/hook/ecrecover.rs)
- [pico/vm/src/emulator/riscv/hook/ed_decompress.rs](pico/vm/src/emulator/riscv/hook/ed_decompress.rs)
- [pico/vm/src/emulator/riscv/hook/mod.rs](pico/vm/src/emulator/riscv/hook/mod.rs)
- [pico/vm/src/emulator/riscv/memory.rs](pico/vm/src/emulator/riscv/memory.rs)
- [pico/vm/src/emulator/riscv/mod.rs](pico/vm/src/emulator/riscv/mod.rs)
- [pico/vm/src/emulator/riscv/public_values.rs](pico/vm/src/emulator/riscv/public_values.rs)
- [pico/vm/src/emulator/riscv/record.rs](pico/vm/src/emulator/riscv/record.rs)
- [pico/vm/src/emulator/riscv/state.rs](pico/vm/src/emulator/riscv/state.rs)
- [pico/vm/src/emulator/riscv/syscalls/code.rs](pico/vm/src/emulator/riscv/syscalls/code.rs)
- [pico/vm/src/emulator/riscv/syscalls/commit.rs](pico/vm/src/emulator/riscv/syscalls/commit.rs)
- [pico/vm/src/emulator/riscv/syscalls/deferred.rs](pico/vm/src/emulator/riscv/syscalls/deferred.rs)
- [pico/vm/src/emulator/riscv/syscalls/halt.rs](pico/vm/src/emulator/riscv/syscalls/halt.rs)
- [pico/vm/src/emulator/riscv/syscalls/hint.rs](pico/vm/src/emulator/riscv/syscalls/hint.rs)
- [pico/vm/src/emulator/riscv/syscalls/mod.rs](pico/vm/src/emulator/riscv/syscalls/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/ec/event.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/ec/event.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/ec/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/ec/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/add.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/add.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/decompress.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/decompress.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/event.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/event.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/edwards/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/event.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/event.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_addsub.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_addsub.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_mul.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/fp2_mul.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/fp.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/fp.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/fptower/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/keccak256/event.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/keccak256/event.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/keccak256/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/keccak256/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/keccak256/permute.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/keccak256/permute.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/poseidon2/event.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/poseidon2/event.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/poseidon2/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/poseidon2/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/poseidon2/permute.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/poseidon2/permute.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/compress.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/compress.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/event.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/event.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/extend.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/extend.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/sha256/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/uint256/event.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/uint256/event.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/uint256/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/uint256/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/uint256/syscall.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/uint256/syscall.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/add.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/add.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/decompress.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/decompress.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/double.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/double.rs)
- [pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/mod.rs](pico/vm/src/emulator/riscv/syscalls/precompiles/weierstrass/mod.rs)
- [pico/vm/src/emulator/riscv/syscalls/syscall_context.rs](pico/vm/src/emulator/riscv/syscalls/syscall_context.rs)
- [pico/vm/src/emulator/riscv/syscalls/unconstrained.rs](pico/vm/src/emulator/riscv/syscalls/unconstrained.rs)
- [pico/vm/src/emulator/riscv/syscalls/verify.rs](pico/vm/src/emulator/riscv/syscalls/verify.rs)
- [pico/vm/src/emulator/riscv/syscalls/write.rs](pico/vm/src/emulator/riscv/syscalls/write.rs)
- [pico/vm/src/emulator/stdin.rs](pico/vm/src/emulator/stdin.rs)
- [pico/vm/src/instances/chiptype/chiptype_macros/define_chip_type.rs](pico/vm/src/instances/chiptype/chiptype_macros/define_chip_type.rs)
- [pico/vm/src/instances/chiptype/chiptype_macros/enum_chip_type.rs](pico/vm/src/instances/chiptype/chiptype_macros/enum_chip_type.rs)
- [pico/vm/src/instances/chiptype/chiptype_macros/impl_air.rs](pico/vm/src/instances/chiptype/chiptype_macros/impl_air.rs)
- [pico/vm/src/instances/chiptype/chiptype_macros/impl_base_air.rs](pico/vm/src/instances/chiptype/chiptype_macros/impl_base_air.rs)
- [pico/vm/src/instances/chiptype/chiptype_macros/impl_chip_behavior.rs](pico/vm/src/instances/chiptype/chiptype_macros/impl_chip_behavior.rs)
- [pico/vm/src/instances/chiptype/chiptype_macros/mod.rs](pico/vm/src/instances/chiptype/chiptype_macros/mod.rs)
- [pico/vm/src/instances/chiptype/mod.rs](pico/vm/src/instances/chiptype/mod.rs)
- [pico/vm/src/instances/chiptype/recursion_chiptype.rs](pico/vm/src/instances/chiptype/recursion_chiptype.rs)
- [pico/vm/src/instances/chiptype/riscv_chiptype.rs](pico/vm/src/instances/chiptype/riscv_chiptype.rs)
- [pico/vm/src/instances/compiler/mod.rs](pico/vm/src/instances/compiler/mod.rs)
- [pico/vm/src/instances/compiler/onchain_circuit/gnark/builder.rs](pico/vm/src/instances/compiler/onchain_circuit/gnark/builder.rs)
- [pico/vm/src/instances/compiler/onchain_circuit/gnark/mod.rs](pico/vm/src/instances/compiler/onchain_circuit/gnark/mod.rs)
- [pico/vm/src/instances/compiler/onchain_circuit/gnark/witness.rs](pico/vm/src/instances/compiler/onchain_circuit/gnark/witness.rs)
- [pico/vm/src/instances/compiler/onchain_circuit/mod.rs](pico/vm/src/instances/compiler/onchain_circuit/mod.rs)
- [pico/vm/src/instances/compiler/onchain_circuit/stdin.rs](pico/vm/src/instances/compiler/onchain_circuit/stdin.rs)
- [pico/vm/src/instances/compiler/onchain_circuit/utils.rs](pico/vm/src/instances/compiler/onchain_circuit/utils.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/combine/builder.rs](pico/vm/src/instances/compiler/recursion_circuit/combine/builder.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/combine/mod.rs](pico/vm/src/instances/compiler/recursion_circuit/combine/mod.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/compress/builder.rs](pico/vm/src/instances/compiler/recursion_circuit/compress/builder.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/compress/mod.rs](pico/vm/src/instances/compiler/recursion_circuit/compress/mod.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/embed/builder.rs](pico/vm/src/instances/compiler/recursion_circuit/embed/builder.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/embed/mod.rs](pico/vm/src/instances/compiler/recursion_circuit/embed/mod.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/mod.rs](pico/vm/src/instances/compiler/recursion_circuit/mod.rs)
- [pico/vm/src/instances/compiler/recursion_circuit/stdin.rs](pico/vm/src/instances/compiler/recursion_circuit/stdin.rs)
- [pico/vm/src/instances/compiler/riscv_circuit/convert/builder.rs](pico/vm/src/instances/compiler/riscv_circuit/convert/builder.rs)
- [pico/vm/src/instances/compiler/riscv_circuit/convert/mod.rs](pico/vm/src/instances/compiler/riscv_circuit/convert/mod.rs)
- [pico/vm/src/instances/compiler/riscv_circuit/deferred/builder.rs](pico/vm/src/instances/compiler/riscv_circuit/deferred/builder.rs)
- [pico/vm/src/instances/compiler/riscv_circuit/deferred/mod.rs](pico/vm/src/instances/compiler/riscv_circuit/deferred/mod.rs)
- [pico/vm/src/instances/compiler/riscv_circuit/mod.rs](pico/vm/src/instances/compiler/riscv_circuit/mod.rs)
- [pico/vm/src/instances/compiler/riscv_circuit/stdin.rs](pico/vm/src/instances/compiler/riscv_circuit/stdin.rs)
- [pico/vm/src/instances/compiler/shapes/mod.rs](pico/vm/src/instances/compiler/shapes/mod.rs)
- [pico/vm/src/instances/compiler/shapes/recursion_shape.rs](pico/vm/src/instances/compiler/shapes/recursion_shape.rs)
- [pico/vm/src/instances/compiler/shapes/riscv_shape.rs](pico/vm/src/instances/compiler/shapes/riscv_shape.rs)
- [pico/vm/src/instances/compiler/shape_vk_bins/vk_map_bb.bin](pico/vm/src/instances/compiler/shape_vk_bins/vk_map_bb.bin)
- [pico/vm/src/instances/compiler/shape_vk_bins/vk_map_kb.bin](pico/vm/src/instances/compiler/shape_vk_bins/vk_map_kb.bin)
- [pico/vm/src/instances/compiler/simple_circuit/builder.rs](pico/vm/src/instances/compiler/simple_circuit/builder.rs)
- [pico/vm/src/instances/compiler/simple_circuit/mod.rs](pico/vm/src/instances/compiler/simple_circuit/mod.rs)
- [pico/vm/src/instances/compiler/simple_circuit/stdin.rs](pico/vm/src/instances/compiler/simple_circuit/stdin.rs)
- [pico/vm/src/instances/compiler/vk_merkle/builder.rs](pico/vm/src/instances/compiler/vk_merkle/builder.rs)
- [pico/vm/src/instances/compiler/vk_merkle/mod.rs](pico/vm/src/instances/compiler/vk_merkle/mod.rs)
- [pico/vm/src/instances/compiler/vk_merkle/stdin.rs](pico/vm/src/instances/compiler/vk_merkle/stdin.rs)
- [pico/vm/src/instances/compiler/witness.rs](pico/vm/src/instances/compiler/witness.rs)
- [pico/vm/src/instances/configs/embed_bb_bn254_poseidon2.rs](pico/vm/src/instances/configs/embed_bb_bn254_poseidon2.rs)
- [pico/vm/src/instances/configs/embed_kb_bn254_poseidon2.rs](pico/vm/src/instances/configs/embed_kb_bn254_poseidon2.rs)
- [pico/vm/src/instances/configs/mod.rs](pico/vm/src/instances/configs/mod.rs)
- [pico/vm/src/instances/configs/recur_bb_poseidon2.rs](pico/vm/src/instances/configs/recur_bb_poseidon2.rs)
- [pico/vm/src/instances/configs/recur_kb_poseidon2.rs](pico/vm/src/instances/configs/recur_kb_poseidon2.rs)
- [pico/vm/src/instances/configs/recur_m31_poseidon2.rs](pico/vm/src/instances/configs/recur_m31_poseidon2.rs)
- [pico/vm/src/instances/configs/riscv_bb_poseidon2.rs](pico/vm/src/instances/configs/riscv_bb_poseidon2.rs)
- [pico/vm/src/instances/configs/riscv_kb_poseidon2.rs](pico/vm/src/instances/configs/riscv_kb_poseidon2.rs)
- [pico/vm/src/instances/configs/riscv_m31_poseidon2.rs](pico/vm/src/instances/configs/riscv_m31_poseidon2.rs)
- [pico/vm/src/instances/machine/combine.rs](pico/vm/src/instances/machine/combine.rs)
- [pico/vm/src/instances/machine/compress.rs](pico/vm/src/instances/machine/compress.rs)
- [pico/vm/src/instances/machine/convert.rs](pico/vm/src/instances/machine/convert.rs)
- [pico/vm/src/instances/machine/deferred.rs](pico/vm/src/instances/machine/deferred.rs)
- [pico/vm/src/instances/machine/embed.rs](pico/vm/src/instances/machine/embed.rs)
- [pico/vm/src/instances/machine/mod.rs](pico/vm/src/instances/machine/mod.rs)
- [pico/vm/src/instances/machine/riscv.rs](pico/vm/src/instances/machine/riscv.rs)
- [pico/vm/src/instances/machine/simple.rs](pico/vm/src/instances/machine/simple.rs)
- [pico/vm/src/instances/mod.rs](pico/vm/src/instances/mod.rs)
- [pico/vm/src/iter/mod.rs](pico/vm/src/iter/mod.rs)
- [pico/vm/src/iter/rayon/impls.rs](pico/vm/src/iter/rayon/impls.rs)
- [pico/vm/src/iter/rayon/mod.rs](pico/vm/src/iter/rayon/mod.rs)
- [pico/vm/src/iter/single/impls.rs](pico/vm/src/iter/single/impls.rs)
- [pico/vm/src/iter/single/mod.rs](pico/vm/src/iter/single/mod.rs)
- [pico/vm/src/lib.rs](pico/vm/src/lib.rs)
- [pico/vm/src/machine/builder/base.rs](pico/vm/src/machine/builder/base.rs)
- [pico/vm/src/machine/builder/extension.rs](pico/vm/src/machine/builder/extension.rs)
- [pico/vm/src/machine/builder/lookup.rs](pico/vm/src/machine/builder/lookup.rs)
- [pico/vm/src/machine/builder/mod.rs](pico/vm/src/machine/builder/mod.rs)
- [pico/vm/src/machine/builder/permutation.rs](pico/vm/src/machine/builder/permutation.rs)
- [pico/vm/src/machine/builder/public_values.rs](pico/vm/src/machine/builder/public_values.rs)
- [pico/vm/src/machine/builder/range_check.rs](pico/vm/src/machine/builder/range_check.rs)
- [pico/vm/src/machine/builder/recursion.rs](pico/vm/src/machine/builder/recursion.rs)
- [pico/vm/src/machine/builder/riscv_memory.rs](pico/vm/src/machine/builder/riscv_memory.rs)
- [pico/vm/src/machine/builder/scoped.rs](pico/vm/src/machine/builder/scoped.rs)
- [pico/vm/src/machine/builder/septic.rs](pico/vm/src/machine/builder/septic.rs)
- [pico/vm/src/machine/builder/sub_builder.rs](pico/vm/src/machine/builder/sub_builder.rs)
- [pico/vm/src/machine/builder/word.rs](pico/vm/src/machine/builder/word.rs)
- [pico/vm/src/machine/chip.rs](pico/vm/src/machine/chip.rs)
- [pico/vm/src/machine/debug/constraints.rs](pico/vm/src/machine/debug/constraints.rs)
- [pico/vm/src/machine/debug/lookups.rs](pico/vm/src/machine/debug/lookups.rs)
- [pico/vm/src/machine/debug/mod.rs](pico/vm/src/machine/debug/mod.rs)
- [pico/vm/src/machine/extension.rs](pico/vm/src/machine/extension.rs)
- [pico/vm/src/machine/field.rs](pico/vm/src/machine/field.rs)
- [pico/vm/src/machine/folder.rs](pico/vm/src/machine/folder.rs)
- [pico/vm/src/machine/keys.rs](pico/vm/src/machine/keys.rs)
- [pico/vm/src/machine/logger.rs](pico/vm/src/machine/logger.rs)
- [pico/vm/src/machine/lookup.rs](pico/vm/src/machine/lookup.rs)
- [pico/vm/src/machine/machine.rs](pico/vm/src/machine/machine.rs)
- [pico/vm/src/machine/mod.rs](pico/vm/src/machine/mod.rs)
- [pico/vm/src/machine/permutation.rs](pico/vm/src/machine/permutation.rs)
- [pico/vm/src/machine/proof.rs](pico/vm/src/machine/proof.rs)
- [pico/vm/src/machine/prover.rs](pico/vm/src/machine/prover.rs)
- [pico/vm/src/machine/septic/curve.rs](pico/vm/src/machine/septic/curve.rs)
- [pico/vm/src/machine/septic/digest.rs](pico/vm/src/machine/septic/digest.rs)
- [pico/vm/src/machine/septic/extension.rs](pico/vm/src/machine/septic/extension.rs)
- [pico/vm/src/machine/septic/fields/babybear.rs](pico/vm/src/machine/septic/fields/babybear.rs)
- [pico/vm/src/machine/septic/fields/dummy.rs](pico/vm/src/machine/septic/fields/dummy.rs)
- [pico/vm/src/machine/septic/fields/koalabear.rs](pico/vm/src/machine/septic/fields/koalabear.rs)
- [pico/vm/src/machine/septic/fields/mersenne31.rs](pico/vm/src/machine/septic/fields/mersenne31.rs)
- [pico/vm/src/machine/septic/fields/mod.rs](pico/vm/src/machine/septic/fields/mod.rs)
- [pico/vm/src/machine/septic/mod.rs](pico/vm/src/machine/septic/mod.rs)
- [pico/vm/src/machine/septic/tests/babybear.rs](pico/vm/src/machine/septic/tests/babybear.rs)
- [pico/vm/src/machine/septic/tests/koalabear.rs](pico/vm/src/machine/septic/tests/koalabear.rs)
- [pico/vm/src/machine/septic/tests/mersenne31.rs](pico/vm/src/machine/septic/tests/mersenne31.rs)
- [pico/vm/src/machine/septic/tests/mod.rs](pico/vm/src/machine/septic/tests/mod.rs)
- [pico/vm/src/machine/septic/tests/utils.rs](pico/vm/src/machine/septic/tests/utils.rs)
- [pico/vm/src/machine/utils.rs](pico/vm/src/machine/utils.rs)
- [pico/vm/src/machine/verifier.rs](pico/vm/src/machine/verifier.rs)
- [pico/vm/src/machine/witness.rs](pico/vm/src/machine/witness.rs)
- [pico/vm/src/primitives/consts.rs](pico/vm/src/primitives/consts.rs)
- [pico/vm/src/primitives/mod.rs](pico/vm/src/primitives/mod.rs)
- [pico/vm/src/primitives/poseidon2/babybear.rs](pico/vm/src/primitives/poseidon2/babybear.rs)
- [pico/vm/src/primitives/poseidon2/koalabear.rs](pico/vm/src/primitives/poseidon2/koalabear.rs)
- [pico/vm/src/primitives/poseidon2/mersenne31.rs](pico/vm/src/primitives/poseidon2/mersenne31.rs)
- [pico/vm/src/primitives/poseidon2/mod.rs](pico/vm/src/primitives/poseidon2/mod.rs)
- [pico/vm/src/proverchain/combine.rs](pico/vm/src/proverchain/combine.rs)
- [pico/vm/src/proverchain/compress.rs](pico/vm/src/proverchain/compress.rs)
- [pico/vm/src/proverchain/convert.rs](pico/vm/src/proverchain/convert.rs)
- [pico/vm/src/proverchain/deferred.rs](pico/vm/src/proverchain/deferred.rs)
- [pico/vm/src/proverchain/embed.rs](pico/vm/src/proverchain/embed.rs)
- [pico/vm/src/proverchain/mod.rs](pico/vm/src/proverchain/mod.rs)
- [pico/vm/src/proverchain/riscv.rs](pico/vm/src/proverchain/riscv.rs)
- [pico/vm/src/thread/channel.rs](pico/vm/src/thread/channel.rs)
- [pico/vm/src/thread/mod.rs](pico/vm/src/thread/mod.rs)


