#!/bin/bash

# Pico Testing Setup Script
# This script sets up the environment for testing the Pico zkVM project

set -e

echo "🚀 Setting up Pico zkVM testing environment..."

# Check if we're in the right directory
if [ ! -f "pico/Cargo.toml" ]; then
    echo "❌ Error: Please run this script from the repository root directory"
    exit 1
fi

# Set up Rust toolchain
echo "🦀 Setting up Rust toolchain..."
cd pico
rustup override set nightly-2025-08-04
cd ..

# Build Rust dependencies and tests
echo "🔨 Building Rust dependencies..."
cd pico
cargo build --features kb
echo "✅ Rust build completed"

# Build field-ffi library for Go tests
echo "🔧 Building field-ffi library..."
cd gnark/field-ffi
cargo build --release
cd ../..

# Set up library paths for Go tests
export CGO_LDFLAGS="-L$(pwd)/target/release -L$(pwd)/target/debug"
export LD_LIBRARY_PATH="$(pwd)/target/release:$(pwd)/target/debug"

# Install Go dependencies
echo "🐹 Installing Go dependencies..."
cd gnark
go mod download
echo "✅ Go dependencies installed"

cd ..

echo "🧪 Running basic tests..."

# Run a simple Rust test
echo "Testing Rust components..."
cargo test --features kb --lib test_kb_ext_inv -- --nocapture

# Run Go tests (some may fail due to missing witness files, which is expected)
echo "Testing Go components..."
cd gnark
CGO_LDFLAGS="-L$(pwd)/../target/release -L$(pwd)/../target/debug" \
LD_LIBRARY_PATH="$(pwd)/../target/release:$(pwd)/../target/debug" \
go test -v ./poseidon2

cd ..

echo "✅ Setup completed successfully!"
echo ""
echo "📋 Testing Commands:"
echo "  Rust tests:  cd pico && cargo test --features kb --lib"
echo "  Go tests:    cd pico/gnark && CGO_LDFLAGS=\"-L\$(pwd)/../target/release -L\$(pwd)/../target/debug\" LD_LIBRARY_PATH=\"\$(pwd)/../target/release:\$(pwd)/../target/debug\" go test -v ./..."
echo "  Examples:    cd pico && cargo run --features kb --example toy_simple_machine"
echo ""
echo "🎯 Available features: bb (BabyBear), kb (KoalaBear), m31 (Mersenne31)"
echo "   Default feature is 'kb' (KoalaBear)"
